# **「絵でわかる日本語」閱讀體驗增強**

## **功能簡介**

本腳本旨在優化「絵でわかる日本語」([https://www.edewakaru.com/](https://www.edewakaru.com/)) 網站的閱讀體驗。

---

## **核心功能**

### **一、精簡頁面佈局**

- **頁面淨化**：自動移除頁首、頁尾、社群媒體按鈕、排名連結及廣告。
- **佈局優化**：擴大主內容區，固定側邊欄目錄，便於長文瀏覽時跳轉。

### **二、閱讀輔助功能**

- **振假名自動轉換**：自動識別並轉換文章中的「單詞（讀音）」格式為標準振假名格式顯示。轉換結果已校對，但如發現未涵蓋詞彙或轉換錯誤，歡迎提交回饋以持續完善。
- **振假名顯隱切換**：可一鍵切換所有轉換後的振假名的顯示狀態。
- **劃詞朗讀（TTS）**：選中文字後點擊朗讀按鈕，調用瀏覽器語音合成朗讀。系統提供男聲與女聲兩種音色選項，每次朗讀將隨機切換。

### **三、效能與視覺優化**

- **替換高清圖片顯示**：自動替換模糊縮圖為高清原圖，僅在圖片進入視窗時載入，提升載入速度與視覺品質。
- **相關文章延遲載入**：預設滾動至附近才載入「相關文章」等嵌入內容。可設定為「點擊後載入」，減少網路請求。

### **四、自訂設定面板**

頁面右下角設定面板提供以下選項：

- **【ページ最適化】**：腳本總開關，控制所有功能的啟用狀態。_（重新整理後生效）_
- **【振り仮名表示】**：控制振假名顯示或隱藏。_（即時生效）_
- **【関連記事表示】**：設定相關文章載入方式，開啟為自動載入，關閉為點擊後載入。_（重新整理後生效）_
- **【単語選択発音】**：控制劃詞朗讀功能，<b style="color:#A42121">僅支援 Microsoft Edge 瀏覽器</b>。_（即時生效）_

---

## **問題回饋**

如遇以下情況，歡迎透過 Greasy Fork 腳本頁面提交回饋：

- 未涵蓋詞彙或轉換錯誤
- 功能異常
- 改進建議

請盡可能提供以下資訊，便於定位和修復問題：

1. 出現問題的文章連結（URL）
2. 錯誤或異常的具體詞語或區域

您的回饋是腳本持續改進的重要支持，感謝！
