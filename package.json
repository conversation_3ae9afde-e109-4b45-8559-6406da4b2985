{"name": "edewakaru-enhanced", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.13.0", "author": "iPumpkin <https://github.com/ipumpkin17>", "license": "GPL-3.0", "scripts": {"dev": "vite", "build": "vite build", "postbuild": "node tools/compress.js -i dist/edewakaru.user.js -o dist/edewakaru.user.js", "preview": "vite preview", "compress": "node tools/compress.js -i src/edewakaru.js", "update-version": "node tools/update-version.js && git add vite.config.js && git commit -m 'chore: update version'", "publish": "git checkout main && git rebase origin/dev && git push", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "devDependencies": {"@antfu/eslint-config": "^4.17.0", "@babel/generator": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/traverse": "^7.28.0", "@vitest/coverage-v8": "^3.2.4", "chalk": "^5.4.1", "eslint": "^9.31.0", "eslint-plugin-format": "^1.0.1", "jsdom": "^26.1.0", "prettier": "^3.6.2", "vite": "^7.0.4", "vite-plugin-monkey": "^5.0.9", "vitest": "^3.2.4", "yargs": "^18.0.0"}}