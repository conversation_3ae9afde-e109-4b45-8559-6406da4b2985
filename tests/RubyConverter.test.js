/**
 * @vitest-environment jsdom
 */
import { beforeEach, describe, expect, it } from 'vitest'
import { RULES } from '../src/config/rules'
import RubyConverter from '../src/modules/RubyConverter'

describe('rULES', () => {
  beforeEach(() => {
    RubyConverter._registeredWords = new Set()
    RubyConverter._wordBankForRegex = []
    RubyConverter._rubyCache = new Map()
    RubyConverter._htmlPatches = new Map()
    RubyConverter._simpleTextReplacements = new Map()
    RubyConverter.globalRegex = null
    RubyConverter.init(RULES)
  })

  function processHTML(input) {
    // 注意：会让测试标签自闭合，所以 expected 需要手动添加闭合标签
    const container = document.createElement('div')
    container.innerHTML = input
    RubyConverter.applyRubyToContainer(container)
    return container.innerHTML
  }

  describe('tEXT.MANUAL_MARK', () => {
    // https://www.edewakaru.com/archives/********.html
    it('夏バテ防止（なつばてぼうし）', () => {
      const input = '夏バテ防止（なつばてぼうし）'
      const expected = '<ruby>夏<rt>なつ</rt></ruby>バテ<ruby>防止<rt>ぼうし</rt></ruby>'

      expect(processHTML(input)).toBe(expected)
    })

    // https://www.edewakaru.com/archives/********.html
    it('真ん中（まんなか）', () => {
      const input = '〜を真ん中（まんなか）にして'
      const expected = '〜を<ruby>真<rt>ま</rt></ruby>ん<ruby>中<rt>なか</rt></ruby>にして'

      expect(processHTML(input)).toBe(expected)
    })
  })

  describe('tEXT.FULL_REPLACE', () => {
    // https://www.edewakaru.com/archives/22659220.html
    it('逆ギレ（ぎゃくぎれ）', () => {
      const input = '逆ギレ（ぎゃくぎれ）'
      const expected = '<ruby>逆<rt>ぎゃく</rt></ruby>ギレ'

      expect(processHTML(input)).toBe(expected)
    })

    // https://www.edewakaru.com/archives/14477138.html
    it('恥（はず）', () => {
      const input = '恥（はず）ずかしい'
      const expected = '<ruby>恥<rt>は</rt></ruby>ずかしい'

      expect(processHTML(input)).toBe(expected)
    })

    it('頭が痛い（あたまがいたい）', () => {
      const input = '頭が痛い（あたまがいたい）'
      const expected = '<ruby>頭<rt>あたま</rt></ruby>が<ruby>痛<rt>いた</rt></ruby>い'

      expect(processHTML(input)).toBe(expected)
    })

    it('冷める（さめる・自動詞）', () => {
      const input = '冷める（さめる・自動詞）'
      const expected = '<ruby>冷<rt>さ</rt></ruby>める（自動詞）'

      expect(processHTML(input)).toBe(expected)
    })
  })

  describe('hTML.MANUAL_MARK', () => {
    // www.edewakaru.com/archives/15059369.html
    it('エアーコンディショナー（?air conditioner）', () => {
      const input = 'エアーコンディショナー（</span></b><span style="font-family: sans-serif; font-size: 15px;"><b style="background-color: rgb(255, 255, 0);"><span style="color: rgb(255, 0, 0);">air conditioner）'
      const expected = '<ruby>エアーコンディショナー<rt>air conditioner</rt></ruby><span style="font-family: sans-serif; font-size: 15px;"><b style="background-color: rgb(255, 255, 0);"><span style="color: rgb(255, 0, 0);"></span></b></span>'

      expect(processHTML(input)).toBe(expected)
    })
  })

  describe('hTML.FULL_REPLACE', () => {
    // https://www.edewakaru.com/archives/17226324.html
    it('一瞬（いっしゅん<br>）', () => {
      const input = '<span style="background-color: rgb(255, 255, 0); color: rgb(255, 0, 0);">「あっという間（ま）」とは「すごく短（みじか）い時間（じかん）」「一瞬（いっしゅん<br>）」という意味（いみ）の表現（ひょうげん）です。</span>'
      const expected = '<span style="background-color: rgb(255, 255, 0); color: rgb(255, 0, 0);">「あっという<ruby>間<rt>ま</rt></ruby>」とは「すごく<ruby>短<rt>みじか</rt></ruby>い<ruby>時間<rt>じかん</rt></ruby>」「<ruby>一瞬<rt>いっしゅん</rt></ruby>」という<ruby>意味<rt>いみ</rt></ruby>の<ruby>表現<rt>ひょうげん</rt></ruby>です。</span>'

      expect(processHTML(input)).toBe(expected)
    })

    // https://www.edewakaru.com/archives/19457724.html
    it('サボ（さぼ）', () => {
      const input = 'サボ（さぼ）って'
      const expected = '<em><ruby>サボ<rt>さぼ</rt></ruby>って</em>'

      expect(processHTML(input)).toBe(expected)
    })

    // https://www.edewakaru.com/archives/19292281.html
    it('白い（しろい）', () => {
      const input = '白い（<span style="background-color: rgb(204, 204, 204);">しろ</span>い）'
      const expected = '<span style="background-color: rgb(204, 204, 204);"><ruby>白<rt>しろ</rt></ruby></span>い'

      expect(processHTML(input)).toBe(expected)
    })

    it('上（じょう）', () => {
      const input = '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（あんぜんじょう）'
      const expected = '（あんぜん）<em><ruby>上<rt>じょう</rt></ruby></em>'

      expect(processHTML(input)).toBe(expected)
    })
  })

  describe('eXCLUDE.STRINGS', () => {
    // https://www.edewakaru.com/archives/12858387.html
    it('挙句（に）', () => {
      const input = '挙句（に）'
      const expected = '挙句（に）'

      expect(processHTML(input)).toBe(expected)
    })

    it('二人称（あなた）', () => {
      const input = '二人称（あなた）'
      const expected = '二人称（あなた）'

      expect(processHTML(input)).toBe(expected)
    })
  })

  describe('eXCLUDE.PARTICLES', () => {
    // https://www.edewakaru.com/archives/12858387.html
    it('意味（を）表す', () => {
      const input = '意味（を）表す'
      const expected = '意味（を）表す'

      expect(processHTML(input)).toBe(expected)
    })
  })

  describe('kATAKANA', () => {
    // https://www.edewakaru.com/archives/23863445.html
    it('ランニングマシーン（running＋machine）', () => {
      const input = 'ランニングマシーン（running＋machine）'
      const expected = '<ruby>ランニングマシーン<rt>running＋machine</rt></ruby>'

      expect(processHTML(input)).toBe(expected)
    })

    // https://www.edewakaru.com/archives/24759121.html
    it('ワンピース（one piece）', () => {
      const input = 'ワンピース（one piece）'
      const expected = '<ruby>ワンピース<rt>one piece</rt></ruby>'

      expect(processHTML(input)).toBe(expected)
    })

    // https://www.edewakaru.com/archives/14981940.html
    it('oL（オーエル）', () => {
      const input = 'OL（オーエル）'
      const expected = '<ruby>OL<rt>オーエル</rt></ruby>'

      expect(processHTML(input)).toBe(expected)
    })

    // https://www.edewakaru.com/archives/24759121.html
    it('owhite shirt（ホワイトシャツ）', () => {
      const input = 'white shirt（ホワイトシャツ）'
      const expected = '<ruby>white shirt<rt>ホワイトシャツ</rt></ruby>'

      expect(processHTML(input)).toBe(expected)
    })

    // it('フリーサイズ', () => {
    //   const input = 'フリーサイズ'
    //   const expected = '<ruby>フリーサイズ<rt>free＋size</rt></ruby>'

    //   expect(processHTML(input)).toBe(expected)
    // })
  })

  describe('aUTO', () => {
    // https://www.edewakaru.com/archives/14771999.html
    it('かっこいい男性（だんせい）', () => {
      const input = 'かっこいい男性（だんせい）'
      const expected = 'かっこいい<ruby>男性<rt>だんせい</rt></ruby>'

      expect(processHTML(input)).toBe(expected)
    })

    // 半角括号
    it('好(す)き', () => {
      const input = '好(す)き'
      const expected = '<ruby>好<rt>す</rt></ruby>き'

      expect(processHTML(input)).toBe(expected)
    })

    // 有空格
    it('出張 （しゅっちょう）', () => {
      const input = '出張 （しゅっちょう）'
      const expected = '<ruby>出張<rt>しゅっちょう</rt></ruby>'

      expect(processHTML(input)).toBe(expected)
    })

    // 有空格
    it('日々 （ひび）', () => {
      const input = '日々 （ひび）'
      const expected = '<ruby>日々<rt>ひび</rt></ruby>'

      expect(processHTML(input)).toBe(expected)
    })
  })

  describe('lEARN', () => {
    // 动态学习 https://www.edewakaru.com/archives/21550197.html
    it('【目に入る（めにはいる）】', () => {
      const input = '【目に入る（めにはいる）】'
      const expected = '【<ruby>目<rt>め</rt></ruby>に<ruby>入<rt>はい</rt></ruby>る】'

      expect(processHTML(input)).toBe(expected)
    })
  })
})
