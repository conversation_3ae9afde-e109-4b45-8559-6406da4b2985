import { describe, expect } from 'vitest'
import RubyConverter from '../../src/modules/RubyConverter'

describe('rubyConverter 正则表达式测试', () => {
  // 获取 RubyConverter 中的正则表达式
  const regex = RubyConverter._regex

  describe('matchBracketRuby', () => {
    it('应该匹配【】或「」括号中的注音格式', () => {
      const testCases = [
        {
          input: '这是一个【测试（てすと）】例子',
          expected: ['【测试（てすと）】'],
          groups: [['测试', 'てすと', '']],
        },
        {
          input: '这是「汉字（かんじ）」和【日本語（にほんご）】',
          expected: ['「汉字（かんじ）」', '【日本語（にほんご）】'],
          groups: [['汉字', 'かんじ', ''], ['日本語', 'にほんご', '']],
        },
        {
          input: '没有匹配的内容',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.matchBracketRuby))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1]).toBe(groups[i][0]) // 汉字部分
            expect(match[2]).toBe(groups[i][1]) // 读音部分
            expect(match[3]).toBe(groups[i][2]) // 后缀部分
          }
        })
      })
    })
  })

  describe('matchKanjiRuby', () => {
    it('应该匹配汉字后跟括号中的读音', () => {
      const testCases = [
        {
          input: '漢字(かんじ)の例',
          expected: ['漢字(かんじ)'],
          groups: [['漢字', 'かんじ']],
        },
        {
          input: '日本語（にほんご）と中国語（ちゅうごくご）',
          expected: ['日本語（にほんご）', '中国語（ちゅうごくご）'],
          groups: [['日本語', 'にほんご'], ['中国語', 'ちゅうごくご']],
        },
        {
          input: '漢字だけ',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.matchKanjiRuby))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1]).toBe(groups[i][0]) // 汉字部分
            expect(match[2]).toBe(groups[i][1]) // 读音部分
          }
        })
      })
    })
  })

  describe('matchLoanwordRuby', () => {
    it('应该匹配英文后跟括号中的片假名', () => {
      const testCases = [
        {
          input: '１００m（メートル）',
          expected: ['m（メートル）'],
          groups: [['m', 'メートル']],
        },
        {
          input: 'OL（オーエル）',
          expected: ['OL（オーエル）'],
          groups: [['OL', 'オーエル']],
        },
        {
          input: 'Owhite shirt（ホワイトシャツ）',
          expected: ['Owhite shirt（ホワイトシャツ）'],
          groups: [['Owhite shirt', 'ホワイトシャツ']],
        },
        {
          input: '英語なし',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.matchLoanwordRuby))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1].toLowerCase()).toBe(groups[i][0].toLowerCase()) // 英文部分
            expect(match[2]).toBe(groups[i][1]) // 片假名部分
          }
        })
      })
    })
  })

  describe('matchKatakanaRuby', () => {
    it('应该匹配片假名后跟括号中的英文', () => {
      const testCases = [
        {
          input: 'エアーコンディショナー（air conditioner）',
          expected: ['エアーコンディショナー（air conditioner）'],
          groups: [['エアーコンディショナー', 'air conditioner']],
        },
        {
          input: 'サイバーマンデー（Cyber Monday）',
          expected: ['サイバーマンデー（Cyber Monday）'],
          groups: [['サイバーマンデー', 'Cyber Monday']],
        },
        {
          input: 'ランニングマシーン（running＋machine）',
          expected: ['ランニングマシーン（running＋machine）'],
          groups: [['ランニングマシーン', 'running＋machine']],
        },
        {
          input: 'オムライス（omelet+rice）',
          expected: ['オムライス（omelet+rice）'],
          groups: [['オムライス', 'omelet+rice']],
        },
        {
          input: 'サボ（さぼ）る',
          expected: [],
          groups: [],
        },
        {
          input: 'カタカナなし',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.matchKatakanaRuby))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1]).toBe(groups[i][0]) // 片假名部分
            expect(match[2].toLowerCase()).toBe(groups[i][1].toLowerCase()) // 英文部分
          }
        })
      })
    })
  })

  describe('extractCandidates', () => {
    it('应该提取非空白、非标点、非尖括号的文本与其读音', () => {
      const testCases = [
        {
          input: '漢字（かんじ）の例',
          expected: ['漢字（かんじ）'],
          groups: [['漢字', 'かんじ']],
        },
        {
          input: '日本語（にほんご）と中国語（ちゅうごくご）',
          expected: ['日本語（にほんご）', 'と中国語（ちゅうごくご）'],
          groups: [['日本語', 'にほんご'], ['と中国語', 'ちゅうごくご']],
        },
        {
          input: '空白 （くうはく）は除外',
          expected: [],
          groups: [],
        },
        {
          input: '<div>（てすと）は除外</div>',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.extractCandidates))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1]).toBe(groups[i][0]) // 文本部分
            expect(match[2]).toBe(groups[i][1]) // 读音部分
          }
        })
      })
    })
  })

  describe('extractKanjiAndReading & extractReading', () => {
    it('extractKanjiAndReading 应该提取汉字和读音', () => {
      const testCases = [
        {
          input: '漢字（かんじ）',
          expected: ['漢字（かんじ）', '漢字', 'かんじ'],
        },
        {
          input: '日本語（にほんご）',
          expected: ['日本語（にほんご）', '日本語', 'にほんご'],
        },
        {
          input: '没有括号',
          expected: null,
        },
      ]

      testCases.forEach(({ input, expected }) => {
        const match = input.match(regex.extractKanjiAndReading)
        if (expected === null) {
          expect(match).toBeNull()
        }
        else {
          // 只检查前三个元素，忽略其他可能的捕获组
          if (match) {
            expect(match.slice(0, 3)).toEqual(expected)
          }
          else {
            expect(match).toEqual(expected)
          }
        }
      })
    })

    it('extractReading 应该只提取读音部分', () => {
      const testCases = [
        {
          input: '漢字（かんじ）',
          expected: ['（かんじ）', 'かんじ'],
        },
        {
          input: '日本語（にほんご）',
          expected: ['（にほんご）', 'にほんご'],
        },
        {
          input: '没有括号',
          expected: null,
        },
      ]

      testCases.forEach(({ input, expected }) => {
        const match = input.match(regex.extractReading)
        if (expected === null) {
          expect(match).toBeNull()
        }
        else {
          // 只检查前两个元素，忽略其他可能的捕获组
          if (match) {
            expect(match.slice(0, 2)).toEqual(expected)
          }
          else {
            expect(match).toEqual(expected)
          }
        }
      })
    })
  })

  describe('测试验证正则', () => {
    it('testNonKana 应该检测非假名字符', () => {
      expect(regex.testNonKana.test('あいうえお')).toBe(false)
      expect(regex.testNonKana.test('アイウエオ')).toBe(false)
      expect(regex.testNonKana.test('あア')).toBe(false)
      expect(regex.testNonKana.test('漢字')).toBe(true)
      expect(regex.testNonKana.test('abc')).toBe(true)
      expect(regex.testNonKana.test('あa')).toBe(true)
    })

    it('testHtmlTag 应该检测HTML标签', () => {
      // 由于在浏览器环境中测试，我们需要重置正则表达式的 lastIndex
      const resetAndTest = (regex, str) => {
        regex.lastIndex = 0
        return regex.test(str)
      }

      expect(resetAndTest(regex.testHtmlTag, '<div>')).toBe(true)
      expect(resetAndTest(regex.testHtmlTag, '<span class="test">')).toBe(true)
      expect(resetAndTest(regex.testHtmlTag, '普通文本')).toBe(false)
      expect(resetAndTest(regex.testHtmlTag, '< 不是标签')).toBe(false)
    })

    it('testRubyTag 应该检测Ruby标签', () => {
      expect(regex.testRubyTag.test('<ruby>')).toBe(true)
      expect(regex.testRubyTag.test('<ruby class="test">')).toBe(true)
      expect(regex.testRubyTag.test('<div>')).toBe(false)
      expect(regex.testRubyTag.test('普通文本')).toBe(false)
    })
  })
})
