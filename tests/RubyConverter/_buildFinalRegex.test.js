/**
 * @vitest-environment jsdom
 */
import { beforeEach, describe, expect, it } from 'vitest'
import { RULES } from '../../src/config/rules'
import RubyConverter from '../../src/modules/RubyConverter'

describe('rubyConverter._buildFinalRegex 方法', () => {
  // 每个测试前重置状态
  beforeEach(() => {
    // 重置 RubyConverter 内部状态
    RubyConverter._registeredWords = new Set()
    RubyConverter._wordBankForRegex = []
    RubyConverter._rubyCache = new Map()
    RubyConverter._htmlPatches = new Map()
    RubyConverter._simpleTextReplacements = new Map()
    RubyConverter.globalRegex = null
  })

  it('应在没有词条时创建null正则', () => {
    // 确保 _wordBankForRegex 为空
    RubyConverter._wordBankForRegex = []

    // 构建正则
    RubyConverter._buildFinalRegex()

    // 验证结果为 null
    expect(RubyConverter.globalRegex).toBe(null)
  })

  it('应根据单个词条创建正则', () => {
    // 添加一个词条
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('测试（てすと）'))

    // 构建正则
    RubyConverter._buildFinalRegex()

    // 验证正则存在并且是正则表达式实例
    expect(RubyConverter.globalRegex).toBeInstanceOf(RegExp)

    // 验证正则能匹配词条
    expect('这是测试（てすと）文本'.match(RubyConverter.globalRegex)).not.toBeNull()
    expect('这是测试（てすと）文本'.match(RubyConverter.globalRegex)[0]).toBe('测试（てすと）')
  })

  it('应根据多个词条创建复合正则', () => {
    // 添加多个词条
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('测试1（てすと1）'))
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('测试2（てすと2）'))

    // 构建正则
    RubyConverter._buildFinalRegex()

    // 验证正则存在
    expect(RubyConverter.globalRegex).toBeInstanceOf(RegExp)

    // 验证正则能匹配所有词条
    expect('这是测试1（てすと1）文本'.match(RubyConverter.globalRegex)[0]).toBe('测试1（てすと1）')
    expect('这是测试2（てすと2）文本'.match(RubyConverter.globalRegex)[0]).toBe('测试2（てすと2）')
  })

  it('应创建包含全局标志的正则', () => {
    // 添加词条
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('测试（てすと）'))

    // 构建正则
    RubyConverter._buildFinalRegex()

    // 验证正则有全局标志
    expect(RubyConverter.globalRegex.global).toBe(true)
  })

  it('应创建能匹配多个实例的正则', () => {
    // 添加词条
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('测试（てすと）'))

    // 构建正则
    RubyConverter._buildFinalRegex()

    // 创建包含多个匹配的文本
    const text = '这是测试（てすと）和另一个测试（てすと）'

    // 使用正则匹配所有实例
    const matches = []
    // 使用字符串的match方法配合g标志来获取所有匹配
    const allMatches = text.match(RubyConverter.globalRegex)
    if (allMatches) {
      matches.push(...allMatches)
    }

    // 验证找到两个匹配
    expect(matches.length).toBe(2)
    expect(matches[0]).toBe('测试（てすと）')
    expect(matches[1]).toBe('测试（てすと）')
  })

  it('应处理包含正则特殊字符的词条', () => {
    // 添加包含特殊字符的词条
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('测试(特殊)字符（てすと）'))

    // 构建正则
    RubyConverter._buildFinalRegex()

    // 验证正则能匹配特殊字符
    expect('这是测试(特殊)字符（てすと）文本'.match(RubyConverter.globalRegex)[0]).toBe('测试(特殊)字符（てすと）')
  })

  it('应正确处理与常见规则集成', () => {
    // 初始化RubyConverter加载规则
    RubyConverter.init(RULES)

    // 保存初始的正则
    const initialRegex = RubyConverter.globalRegex

    // 添加一个新词条
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('新词条（しんことう）'))

    // 重建正则
    RubyConverter._buildFinalRegex()

    // 验证新正则不同于初始正则
    expect(RubyConverter.globalRegex).not.toBe(initialRegex)
    expect(RubyConverter.globalRegex.toString()).not.toBe(initialRegex.toString())

    // 验证新正则能匹配新词条
    expect('这是新词条（しんことう）'.match(RubyConverter.globalRegex)[0]).toBe('新词条（しんことう）')
  })

  it('应能正确匹配RegisterWord注册的词条', () => {
    // 通过RegisterWord方法注册词条
    RubyConverter.registerWord({
      pattern: '注册词条（とうろく）',
      kanji: '注册词条',
      reading: 'とうろく',
      source: '测试来源',
    })

    // 重建正则
    RubyConverter._buildFinalRegex()

    // 验证正则能匹配注册的词条
    expect('这是注册词条（とうろく）文本'.match(RubyConverter.globalRegex)[0]).toBe('注册词条（とうろく）')
  })

  it('应处理动态添加多个词条后的正则更新', () => {
    // 初始构建
    RubyConverter._buildFinalRegex()

    // 第一次添加
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('词条1（ことう1）'))
    RubyConverter._buildFinalRegex()
    const regex1 = RubyConverter.globalRegex

    // 第二次添加
    RubyConverter._wordBankForRegex.push(RubyConverter._escapeRegExp('词条2（ことう2）'))
    RubyConverter._buildFinalRegex()
    const regex2 = RubyConverter.globalRegex

    // 验证两次正则不同
    expect(regex1).not.toBe(regex2)
    expect(regex1.toString()).not.toBe(regex2.toString())

    // 验证第二个正则能匹配两个词条
    expect('词条1（ことう1）和词条2（ことう2）'.match(regex2)[0]).toBe('词条1（ことう1）')
  })
})
