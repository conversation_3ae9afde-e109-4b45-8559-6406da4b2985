/**
 * @vitest-environment jsdom
 */
import { describe, expect, it } from 'vitest'
import RubyConverter from '../../src/modules/RubyConverter'

describe('rubyConverter 工具方法', () => {
  // 每个测试前重置状态

  describe('_escapeRegExp 方法', () => {
    it('应该正确转义正则表达式特殊字符', () => {
      const testCases = [
        { input: 'hello.world', expected: 'hello\\.world' },
        { input: 'test(123)', expected: 'test\\(123\\)' },
        { input: '[abc]+', expected: '\\[abc\\]\\+' },
        { input: '漢字(かんじ)', expected: '漢字\\(かんじ\\)' },
        { input: '特殊字符.*+?^${}()|[]\\', expected: '特殊字符\\.\\*\\+\\?\\^\\$\\{\\}\\(\\)\\|\\[\\]\\\\' },
      ]

      testCases.forEach(({ input, expected }) => {
        // 调用 RubyConverter 的私有方法
        const result = RubyConverter._escapeRegExp(input)
        expect(result).toBe(expected)
      })
    })

    it('应该使转义后的字符串在正则表达式中按字面意思匹配', () => {
      const specialChars = '.+*?^$()[]{}|\\'
      const escaped = RubyConverter._escapeRegExp(specialChars)

      // 创建一个使用转义后字符串的正则表达式，添加 ^ 和 $ 锚点确保匹配整个字符串
      const regex = new RegExp(`^${escaped}$`)

      // 应该精确匹配原始字符串
      expect(regex.test(specialChars)).toBe(true)

      // 不应该匹配其他字符串
      expect(regex.test('something else')).toBe(false)
      expect(regex.test(`${specialChars}extra`)).toBe(false)
    })
  })

  describe('_katakanaToHiragana 方法', () => {
    it('应正确将片假名转换为平假名', () => {
      const result = RubyConverter._katakanaToHiragana('テスト')
      expect(result).toBe('てすと')
    })

    it('应正确处理混合片假名和平假名的字符串', () => {
      const result = RubyConverter._katakanaToHiragana('テストです')
      expect(result).toBe('てすとです')
    })

    it('应正确处理包含长音符号的片假名', () => {
      const result = RubyConverter._katakanaToHiragana('コーヒー')
      expect(result).toBe('こーひー')
    })

    it('应正确处理包含非假名字符的字符串', () => {
      const result = RubyConverter._katakanaToHiragana('テスト123')
      expect(result).toBe('てすと123')
    })

    it('应正确处理空字符串', () => {
      const result = RubyConverter._katakanaToHiragana('')
      expect(result).toBe('')
    })

    it('应正确处理null或undefined值', () => {
      expect(RubyConverter._katakanaToHiragana(null)).toBe('')
      expect(RubyConverter._katakanaToHiragana(undefined)).toBe('')
    })
  })

  describe('_isKanaChar 方法', () => {
    it('应正确识别平假名字符', () => {
      expect(RubyConverter._isKanaChar('あ')).toBe(true)
      expect(RubyConverter._isKanaChar('き')).toBe(true)
      expect(RubyConverter._isKanaChar('ん')).toBe(true)
    })

    it('应正确识别片假名字符', () => {
      expect(RubyConverter._isKanaChar('ア')).toBe(true)
      expect(RubyConverter._isKanaChar('キ')).toBe(true)
      expect(RubyConverter._isKanaChar('ン')).toBe(true)
    })

    it('应正确识别假名特殊符号', () => {
      expect(RubyConverter._isKanaChar('ー')).toBe(true) // 长音符号
      expect(RubyConverter._isKanaChar('ッ')).toBe(true) // 促音
    })

    it('应正确拒绝非假名字符', () => {
      expect(RubyConverter._isKanaChar('a')).toBe(false)
      expect(RubyConverter._isKanaChar('1')).toBe(false)
      expect(RubyConverter._isKanaChar('漢')).toBe(false)
      expect(RubyConverter._isKanaChar('!')).toBe(false)
    })

    it('应正确拒绝多字符字符串', () => {
      expect(RubyConverter._isKanaChar('あい')).toBe(false)
      expect(RubyConverter._isKanaChar('アイ')).toBe(false)
    })

    it('应正确拒绝空字符串', () => {
      expect(RubyConverter._isKanaChar('')).toBe(false)
    })
  })

  describe('_hasInvalidChars 方法', () => {
    it('应正确识别包含无效字符的字符串', () => {
      expect(RubyConverter._hasInvalidChars('あア漢字a')).toBe(true)
      expect(RubyConverter._hasInvalidChars('あア漢字123')).toBe(true)
      expect(RubyConverter._hasInvalidChars('あア漢字!')).toBe(true)
    })

    it('应正确接受只包含有效字符的字符串', () => {
      expect(RubyConverter._hasInvalidChars('あアいイ')).toBe(false)
      expect(RubyConverter._hasInvalidChars('漢字かな')).toBe(false)
      expect(RubyConverter._hasInvalidChars('一二三')).toBe(false)
    })

    it('应正确处理空字符串', () => {
      expect(RubyConverter._hasInvalidChars('')).toBe(false)
    })
  })

  describe('_isAllKatakana 方法', () => {
    it('应正确识别普通片假名', () => {
      expect(RubyConverter._isAllKatakana('カタカナ')).toBe(true)
      expect(RubyConverter._isAllKatakana('アイウエオ')).toBe(true)
    })

    it('应正确识别包含长音符号的片假名', () => {
      expect(RubyConverter._isAllKatakana('コーヒー')).toBe(true)
      expect(RubyConverter._isAllKatakana('ラーメン')).toBe(true)
      expect(RubyConverter._isAllKatakana('スマートフォン')).toBe(true)
    })

    it('应拒绝包含非片假名字符的字符串', () => {
      expect(RubyConverter._isAllKatakana('カタカナひらがな')).toBe(false)
      expect(RubyConverter._isAllKatakana('カタカナ漢字')).toBe(false)
      expect(RubyConverter._isAllKatakana('カタカナABC')).toBe(false)
    })

    it('应拒绝空字符串或null值', () => {
      expect(RubyConverter._isAllKatakana('')).toBe(false)
      expect(RubyConverter._isAllKatakana(null)).toBe(false)
      expect(RubyConverter._isAllKatakana(undefined)).toBe(false)
    })
  })
})
