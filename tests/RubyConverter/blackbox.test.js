/**
 * @vitest-environment jsdom
 */
import { beforeEach, describe, expect, it } from 'vitest'
import { RULES } from '../../src/config/rules'
import RubyConverter from '../../src/modules/RubyConverter'

describe('rubyConverter 黑盒测试', () => {
  beforeEach(() => {
    // 重置 RubyConverter 状态
    RubyConverter._registeredWords = new Set()
    RubyConverter._wordBankForRegex = []
    RubyConverter._rubyCache = new Map()
    RubyConverter._htmlPatches = new Map()
    RubyConverter._simpleTextReplacements = new Map()
    RubyConverter.globalRegex = null

    // 初始化 RubyConverter
    RubyConverter.init(RULES)
  })

  /**
   * 辅助函数：将输入字符串转换为DOM，应用RubyConverter，然后返回处理后的HTML
   * @param {string} input - 输入HTML字符串
   * @returns {string} - 处理后的HTML字符串
   */
  function processHTML(input) {
    // 创建一个容器
    const container = document.createElement('div')
    container.innerHTML = input

    // 应用RubyConverter
    RubyConverter.applyRubyToContainer(container)

    // 返回处理后的HTML
    return container.innerHTML
  }

  describe('完整流程测试', () => {
    it('应该正确处理带有【】括号的注音', () => {
      const input = '<b><span style="font-size: 125%;">【割り勘（わりかん）】</span></b>'
      const expected = '<b><span style="font-size: 125%;">【<ruby>割<rt>わ</rt></ruby>り<ruby>勘<rt>かん</rt></ruby>】</span></b>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理带有「」括号的注音', () => {
      const input = '<p>「食べ物（たべもの）」について話しましょう。</p>'
      const expected = '<p>「<ruby>食<rt>た</rt></ruby>べ<ruby>物<rt>もの</rt></ruby>」について話しましょう。</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理片假名模式', () => {
      const input = '<p>computer（コンピューター）が好きです。</p>'
      const expected = '<p><ruby>computer<rt>コンピューター</rt></ruby>が好きです。</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理反向片假名模式', () => {
      const input = '<p>コンピューター（computer）が好きです。</p>'
      const expected = '<p><ruby>コンピューター<rt>computer</rt></ruby>が好きです。</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理包含长音符号的片假名', () => {
      const input = '<p>コーヒー（coffee）が好きです。</p>'
      const expected = '<p><ruby>コーヒー<rt>coffee</rt></ruby>が好きです。</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理 HTML.MANUAL_MARK 规则', () => {
      const input = '<p>真っ赤（まっか）な顔</p>'
      // 实际上 RubyConverter 会分解处理 "真っ赤"
      const expected = '<p><ruby>真<rt>ま</rt></ruby>っ<ruby>赤<rt>か</rt></ruby>な顔</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理 HTML.FULL_REPLACE 规则', () => {
      const input = '<p>復習（ふくしゅう<br>）</p>'
      // 根据实际行为，HTML.FULL_REPLACE会被转换为ruby标签，而不是保持原样
      const expected = '<p><ruby>復習<rt>ふくしゅう</rt></ruby></p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    // https://www.edewakaru.com/archives/17226324.html
    it('应该正确处理 HTML.FULL_REPLACE 规则，实际', () => {
      const input = '<span style="background-color: rgb(255, 255, 0); color: rgb(255, 0, 0);">「あっという間（ま）」とは「すごく短（みじか）い時間（じかん）」「一瞬（いっしゅん<br>）」という意味（いみ）の表現（ひょうげん）です。</span>'
      const expected = '<span style="background-color: rgb(255, 255, 0); color: rgb(255, 0, 0);">「あっという<ruby>間<rt>ま</rt></ruby>」とは「すごく<ruby>短<rt>みじか</rt></ruby>い<ruby>時間<rt>じかん</rt></ruby>」「<ruby>一瞬<rt>いっしゅん</rt></ruby>」という<ruby>意味<rt>いみ</rt></ruby>の<ruby>表現<rt>ひょうげん</rt></ruby>です。</span>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理 TEXT.MANUAL_MARK 规则', () => {
      const input = '<p>使い方（つかいかた）</p>'
      const expected = '<p><ruby>使<rt>つか</rt></ruby>い<ruby>方<rt>かた</rt></ruby></p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理 TEXT.FULL_REPLACE 规则', () => {
      const input = '<p>羽根を伸ばす（羽根を伸ばす）</p>'
      // 根据实际行为，TEXT.FULL_REPLACE 会被转换为 ruby 标签
      const expected = '<p><ruby>羽根<rt>はね</rt></ruby>を<ruby>伸<rt>の</rt></ruby>ばす</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理多个注音在同一文本中', () => {
      const input = '<p>日本語（にほんご）の勉強（べんきょう）が好きです。</p>'
      const expected = '<p><ruby>日本語<rt>にほんご</rt></ruby>の<ruby>勉強<rt>べんきょう</rt></ruby>が好きです。</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理嵌套的HTML结构', () => {
      const input = '<div><p>日本語（にほんご）</p><p>の<span>勉強（べんきょう）</span>が好きです。</p></div>'
      const expected = '<div><p><ruby>日本語<rt>にほんご</rt></ruby></p><p>の<span><ruby>勉強<rt>べんきょう</rt></ruby></span>が好きです。</p></div>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理动态学习的词汇', () => {
      // 第一次处理，学习新词
      const input1 = '<p>【新単語（しんたんご）】を覚えましょう。</p>'
      processHTML(input1)

      // 第二次处理，应用学到的词汇
      const input2 = '<p>新単語（しんたんご）を覚えましょう。</p>'
      const expected2 = '<p><ruby>新単語<rt>しんたんご</rt></ruby>を覚えましょう。</p>'

      const result2 = processHTML(input2)
      expect(result2).toBe(expected2)
    })

    it('应该跳过处理排除列表中的词条', () => {
      // 假设 "挙句（に）" 在排除列表中
      const input = '<p>挙句（に）の果て</p>'
      const expected = '<p>挙句（に）の果て</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })

    it('应该正确处理包含特殊字符的文本', () => {
      const input = '<p>１０日（とおか）後に会いましょう。</p>'
      const expected = '<p><ruby>１０日<rt>とおか</rt></ruby>後に会いましょう。</p>'

      const result = processHTML(input)
      expect(result).toBe(expected)
    })
  })
})
