/**
 * @vitest-environment jsdom
 */
import { describe, expect, it } from 'vitest'
import RubyConverter from '../../src/modules/RubyConverter'

describe('rubyConverter._extractRubyCandidates 测试', () => {
  it('一瞬（いっしゅん）', () => {
    const input = '一瞬（いっしゅん）'
    const results = RubyConverter._extractRubyCandidates(input)
    expect(results.length).toBe(1)
    expect(results[0]).toEqual({
      kanji: '一瞬',
      reading: 'いっしゅん',
      fullPattern: '一瞬（いっしゅん）',
    })
  })

  it('れきし<em>上（じょう）</em>', () => {
    const input = 'れきし<em>上（じょう）</em>'
    const results = RubyConverter._extractRubyCandidates(input)
    expect(results.length).toBe(1)
    expect(results[0]).toEqual({
      kanji: '上',
      reading: 'じょう',
      fullPattern: '上（じょう）',
    })
  })

  it('冷める（さめる）（自動詞）', () => {
    const input = '冷める（さめる）（自動詞）'
    const results = RubyConverter._extractRubyCandidates(input)
    expect(results.length).toBe(1) // 注意：这里应该是1，因为第二个匹配的kanji为空会被跳过
    expect(results[0]).toEqual({
      kanji: '冷める',
      reading: 'さめる',
      fullPattern: '冷める（さめる）',
    })
  })

  it('頭（あたま）が痛（いた）い', () => {
    const input = '頭（あたま）が痛（いた）い'
    const results = RubyConverter._extractRubyCandidates(input)
    expect(results.length).toBe(2)
    expect(results[0]).toEqual({
      kanji: '頭',
      reading: 'あたま',
      fullPattern: '頭（あたま）',
    })
    // 注册不了会交给最后一步匹配汉字 ruby
    expect(results[1]).toEqual({
      kanji: 'が痛',
      reading: 'いた',
      fullPattern: 'が痛（いた）',
    })
  })

  it('目に余る（めにあまる）②', () => {
    const input = '目に余る（めにあまる）②'
    const results = RubyConverter._extractRubyCandidates(input)
    expect(results.length).toBe(1)
    expect(results[0]).toEqual({
      kanji: '目に余る',
      reading: 'めにあまる',
      fullPattern: '目に余る（めにあまる）',
    })
  })

  it('<b>３日（みっか）</b></span>', () => {
    const input = '<b>３日（みっか）</b></span>'
    const results = RubyConverter._extractRubyCandidates(input)
    expect(results.length).toBe(1)
    expect(results[0]).toEqual({
      kanji: '３日',
      reading: 'みっか',
      fullPattern: '３日（みっか）',
    })
  })

  it('<b style="background-color: rgb(255, 255, 0);">１（いち）</b>日（にち）', () => {
    const input = '<b style="background-color: rgb(255, 255, 0);">１（いち）</b>日（にち）'
    const results = RubyConverter._extractRubyCandidates(input)
    expect(results.length).toBe(2)
    expect(results[0]).toEqual({
      kanji: '１',
      reading: 'いち',
      fullPattern: '１（いち）',
    })
    expect(results[1]).toEqual({
      kanji: '日',
      reading: 'にち',
      fullPattern: '日（にち）',
    })
  })
})
