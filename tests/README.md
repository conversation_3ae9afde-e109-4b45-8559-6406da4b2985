# UserScripts 测试架构

## 概述

本项目采用 Vitest 作为测试框架，按照模块和功能进行测试的组织，每个测试文件专注于一个特定的功能点或方法。

## 测试结构

测试文件按照以下方式组织：

```
tests/
├── integration/                # 集成测试
│   ├── RubyConverter/          # RubyConverter 模块的集成测试
│   │   └── blackbox.test.js    # 黑盒测试
│   └── ...                     # 其他模块的集成测试
├── unit/                       # 单元测试
│   ├── RubyConverter/          # RubyConverter 模块的单元测试
│   │   ├── _applyHtmlPatches.test.js
│   │   ├── _applyTextReplacements.test.js
│   │   ├── _buildFinalRegex.test.js
│   │   ├── _compileStaticRules.test.js
│   │   ├── _learnFromContainer.test.js
│   │   ├── _parseFurigana.test.js
│   │   ├── _processTextNodes.test.js
│   │   ├── registerWord.test.js
│   │   └── utils.test.js
│   └── ...                     # 其他模块的单元测试
└── README.md                   # 测试文档（本文件）
```

### 集成测试

集成测试关注模块的整体功能，测试多个组件协同工作的情况：

- `integration/RubyConverter/blackbox.test.js`：RubyConverter 的整体功能测试

### 单元测试

单元测试针对特定方法或功能点进行测试，通过模拟依赖来隔离被测试的功能：

- `unit/RubyConverter/_applyTextReplacements.test.js`：测试文本替换功能
- `unit/RubyConverter/_parseFurigana.test.js`：测试振假名解析功能
- `unit/RubyConverter/_compileStaticRules.test.js`：测试规则编译功能
- `unit/RubyConverter/_learnFromContainer.test.js`：测试从容器学习词条功能
- `unit/RubyConverter/_applyHtmlPatches.test.js`：测试HTML补丁应用功能
- `unit/RubyConverter/_buildFinalRegex.test.js`：测试正则表达式构建功能
- `unit/RubyConverter/_processTextNodes.test.js`：测试文本节点处理功能
- `unit/RubyConverter/registerWord.test.js`：测试词条注册功能
- `unit/RubyConverter/utils.test.js`：测试工具方法

## 测试原则

1. **隔离性**：每个测试应该独立于其他测试，不依赖其他测试的执行结果
2. **明确性**：每个测试应该有明确的断言，表明期望的结果
3. **边界条件**：测试应该包括正常情况、边界条件和错误情况
4. **单一职责**：每个测试只测试一个方面的功能

## 运行测试

使用以下命令运行所有测试：

```bash
pnpm test
```

运行单个测试文件：

```bash
pnpm test tests/unit/RubyConverter/_applyTextReplacements.test.js
```

运行特定模块的所有测试：

```bash
pnpm test tests/unit/RubyConverter
```

只运行集成测试：

```bash
pnpm test tests/integration
```

只运行单元测试：

```bash
pnpm test tests/unit
```

## 测试代码风格

1. 测试名称应描述性强，表明测试的功能和期望结果
2. 使用 `describe` 和 `it` 来组织测试
3. 在 `beforeEach` 中设置测试环境，确保每个测试都有一个干净的环境
4. 使用 `afterEach` 清理测试中的临时资源
5. 使用 `vi.fn()` 来模拟函数和依赖

## 测试覆盖率

运行以下命令可以生成测试覆盖率报告：

```bash
pnpm test:coverage
```

覆盖率报告会生成在 `coverage` 目录下，可以通过浏览器查看 HTML 格式的报告。
