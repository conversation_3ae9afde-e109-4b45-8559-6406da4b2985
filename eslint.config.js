import antfu from '@antfu/eslint-config'

export default antfu(
  {
    formatters: {
      markdown: 'prettier',
    },
    ignores: [ 'eslint.config.js', 'vite.config.js', 'src/edewakaru.js'],
  },
  {
    languageOptions: {
      globals: {
        GM_addStyle: 'readonly',
        GM_getValue: 'readonly',
        GM_setValue: 'readonly',
      },
    },
  },
  {
    rules: {
      'no-console': 'off',
      'no-undef': 'off',
      'no-unused-vars': 'off',
      'no-use-before-define': 'off',
      'regexp/no-obscure-range': 'warn',
      'regexp/no-super-linear-backtracking': 'warn',
    },
  },
)
