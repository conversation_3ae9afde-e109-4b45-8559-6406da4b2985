# **「絵でわかる日本語」阅读体验增强**

## **功能简介**

本脚本旨在优化「絵でわかる日本語」([https://www.edewakaru.com/](https://www.edewakaru.com/)) 网站的阅读体验。

---

## **核心功能**

### **一、精简页面布局**

- **页面净化**：自动移除页眉、页脚、社交媒体按钮、排名链接及广告。
- **布局优化**：扩大主内容区，固定侧边栏目录，便于长文浏览时跳转。

### **二、阅读辅助功能**

- **振假名自动转换**：自动识别并转换文章中的 "单词（读音）" 格式为标准振假名格式显示。转换结果已校对，但如发现未覆盖词汇或转换错误，欢迎提交反馈以持续完善。
- **振假名显隐切换**：可一键切换所有转换后的振假名的显示状态。
- **划词朗读（TTS）**：选中文本后点击朗读按钮，系统将调用浏览器语音合成功能进行朗读。支持男声与女声两种音色，每次朗读自动随机切换。

### **三、性能与视觉优化**

- **替换高清图片显示**：自动替换模糊缩略图为高清原图，仅在图片进入视窗时加载，提升加载速度与视觉质量。
- **相关文章延迟加载**：默认滚动至附近才加载“相关文章”等内嵌内容。可设置为“点击后加载”，减少网络请求。

### **四、自定义设置面板**

页面右下角设置面板提供以下选项：

- **【ページ最適化】**：脚本总开关，控制所有功能的启用状态。_（刷新后生效）_
- **【振り仮名表示】**：控制振假名显示或隐藏。_（即时生效）_
- **【関連記事表示】**：设置相关文章加载方式，开启为自动加载，关闭为点击后加载。_（刷新后生效）_
- **【単語選択発音】**：控制划词朗读功能，<b style="color:#A42121">仅支持 Microsoft Edge 浏览器</b>。_（即时生效）_

---

## **问题反馈**

如遇以下情况，欢迎通过 Greasy Fork 脚本页面提交反馈：

- 振假名转换错误或未覆盖的词汇
- 功能异常
- 改进建议

请尽可能提供以下信息，便于定位和修复问题：

1. 出现问题的文章链接（URL）
2. 错误或异常的具体词语或区域

您的反馈是脚本持续改进的重要支持，感谢！
