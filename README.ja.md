# **「絵でわかる日本語」読書体験向上スクリプト**

## **機能概要**

このスクリプトは「絵でわかる日本語」([https://www.edewakaru.com/](https://www.edewakaru.com/)) サイトの読書体験を最適化することを目的としています。

---

## **主要機能**

### **一、ページレイアウトの簡素化**

- **ページの浄化**：ヘッダー、フッター、ソーシャルメディアボタン、ランキングリンク、広告を自動削除。
- **レイアウト最適化**：メインコンテンツエリアを拡大し、サイドバーの目次を固定して長文閲覧時のナビゲーションを改善。

### **二、読書支援機能**

- **振り仮名自動変換**：記事内の「単語（読み方）」形式を自動認識し、標準的な振り仮名表示に変換。変換結果は校正済みですが、未対応の語彙や変換の誤りを発見された場合は、継続的な改善のためにフィードバックをお寄せください。
- **振り仮名表示切替**：変換されたすべての振り仮名の表示状態をワンクリックで切り替え可能。
- **選択テキスト読み上げ（TTS）**：テキストを選択後、読み上げボタンをクリックしてブラウザの音声合成機能で読み上げ。システムは男性と女性の両方の音声オプションを提供し、読み上げごとにランダムに切り替わります。

### **三、パフォーマンスと視覚的最適化**

- **高解像度画像表示**：ぼやけたサムネイルを高解像度の原画像に自動置換。画像がビューポートに入った時のみ読み込み、読み込み速度と視覚品質を向上。
- **関連記事の遅延読み込み**：デフォルトでは近くまでスクロールした時に「関連記事」などの埋め込みコンテンツを読み込み。「クリック後読み込み」に設定可能で、ネットワークリクエストを削減。

### **四、カスタム設定パネル**

ページ右下に設定パネルを提供し、以下の設定項目に対応：

- **【ページ最適化】**：スクリプト機能の総合スイッチ。他の設定はこの項目に基づいて有効化。_（ページ再読み込み後に反映）_
- **【振り仮名表示】**：振り仮名表示状態の切り替え。_（即座に反映）_
- **【関連記事表示】**：「関連記事」の読み込みモード設定。オンの場合は自動読み込み、オフの場合はクリック読み込み。_（ページ再読み込み後に反映）_
- **【単語選択発音】**：選択テキスト読み上げ機能のオン/オフ。<b style="color:#A42121">Microsoft Edge ブラウザのみサポート</b>。_（即座に反映）_

---

## **問題報告**

以下の状況に遭遇した場合は、Greasy Fork スクリプトページからフィードバックをお送りください：

- 振り仮名変換の誤りや未対応の語彙
- 機能の異常
- 改善提案

問題の特定と修正のため、可能な限り以下の情報をご提供ください：

1. 問題が発生した記事のリンク（URL）
2. エラーや異常が発生した具体的な単語や箇所

皆様のフィードバックはスクリプトの継続的な改善にとって重要なサポートです。ありがとうございます！
