# **「絵でわかる日本語」독서 경험 향상**

## **기능 개요**

이 스크립트는「絵でわかる日本語」([https://www.edewakaru.com/](https://www.edewakaru.com/)) 웹사이트의 독서 경험을 최적화하는 것을 목표로 합니다.

---

## **핵심 기능**

### **1. 페이지 레이아웃 간소화**

- **페이지 정리**: 헤더, 푸터, 소셜 미디어 버튼, 랭킹 링크 및 광고를 자동으로 제거합니다.
- **레이아웃 최적화**: 메인 콘텐츠 영역을 확장하고 사이드바 목차를 고정하여 긴 글을 읽을 때 탐색을 용이하게 합니다.

### **2. 독서 지원 기능**

- **후리가나 자동 변환**: 기사 내의 "단어(읽기)" 형식을 자동으로 인식하여 표준 후리가나 형식으로 변환합니다. 변환 결과는 교정되었지만, 누락된 어휘나 변환 오류를 발견하시면 지속적인 개선을 위해 피드백을 제출해 주세요.
- **후리가나 표시 전환**: 변환된 모든 후리가나의 표시 상태를 한 번의 클릭으로 전환할 수 있습니다.
- **텍스트 음성 변환(TTS)**: 텍스트를 선택한 후 읽기 버튼을 클릭하여 브라우저의 음성 합성 기능으로 읽어줍니다. 시스템은 남성과 여성 음성 옵션을 제공하며, 매번 읽기 시 무작위로 전환됩니다.

### **3. 성능 및 시각적 최적화**

- **고해상도 이미지 표시**: 흐릿한 썸네일을 고해상도 원본 이미지로 자동 교체하며, 이미지가 뷰포트에 들어올 때만 로드하여 로딩 속도와 시각적 품질을 향상시킵니다.
- **관련 기사 지연 로딩**: 기본적으로 근처까지 스크롤할 때만 "관련 기사" 등의 임베드 콘텐츠를 로드합니다. "클릭 후 로드"로 설정하여 네트워크 요청을 줄일 수 있습니다.

### **4. 사용자 정의 설정 패널**

페이지 우하단에 설정 패널을 제공하며, 다음 옵션을 지원합니다:

- **【ページ最適化】**: 스크립트의 총 스위치로, 모든 기능의 활성화 상태를 제어합니다. _(새로고침 후 적용)_
- **【振り仮名表示】**: 후리가나 표시 또는 숨김을 제어합니다. _(즉시 적용)_
- **【関連記事表示】**: 관련 기사 로딩 방식을 설정합니다. 활성화 시 자동 로드, 비활성화 시 클릭 후 로드. _(새로고침 후 적용)_
- **【単語選択発音】**: 텍스트 음성 변환 기능을 제어합니다. <b style="color:#A42121">Microsoft Edge 브라우저만 지원</b>. _(즉시 적용)_

---

## **문제 신고**

다음과 같은 상황이 발생하면 Greasy Fork 스크립트 페이지를 통해 피드백을 제출해 주세요:

- 발음 변환 오류 또는 누락된 어휘
- 기능 오작동
- 개선 제안

문제를 찾아 수정하는 데 도움이 되도록 가능한 한 다음 정보를 제공해 주세요:

1. 문제가 발생한 기사 링크(URL)
2. 오류나 이상이 발생한 구체적인 단어나 영역

여러분의 피드백은 이 스크립트의 지속적인 개선을 위한 중요한 지원입니다. 감사합니다!
