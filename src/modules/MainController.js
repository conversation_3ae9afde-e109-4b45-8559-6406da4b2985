/**
 * @module MainController
 * @description 脚本主控制器，负责协调所有模块的初始化和执行流程
 */

import { RULES } from '../config/rules'
import EventBus from '../utils/EventBus'
import ContextMenu from './ContextMenu'
import IframeLoader from './IframeLoader'
import ImageProcessor from './ImageProcessor'
import PageOptimizer from './PageOptimizer'
import RubyConverter from './RubyConverter'
import SettingsPanel from './SettingsPanel'
import TTSPlayer from './TTSPlayer'

const MainController = {
  /**
   * 启动脚本
   */
  run() {
    // 从 SettingsPanel 获取所有持久化的用户设置
    const options = SettingsPanel.getOptions()

    // 步骤 1: 检查脚本是否被用户禁用
    // 如果禁用，则仅加载设置面板，不执行任何页面修改
    if (!options.SCRIPT_ENABLED) {
      document.addEventListener('DOMContentLoaded', () => SettingsPanel.init())
      return
    }

    // 步骤 2: 在 document-start 阶段，立即执行不依赖 DOM 内容的操作，以尽快生效
    PageOptimizer.init() // 注入样式，防止页面闪烁
    RubyConverter.init(RULES) // 预处理词库，为后续操作做准备

    // 初始化 TTSPlayer，让它可以订阅事件
    TTSPlayer.init()

    // 订阅 TTS 开关事件，确保 ContextMenu 正确响应
    this._subscribeToEvents()

    // 步骤 3: 等待 DOM 完全加载后，执行依赖 DOM 内容的操作
    document.addEventListener('DOMContentLoaded', () => {
      PageOptimizer.cleanupGlobalElements()
      IframeLoader.init({ IFRAME_LOAD_ENABLED: options.IFRAME_LOAD_ENABLED })
      SettingsPanel.init()

      // 如果 TTS 功能已启用，则初始化上下文菜单
      if (options.TTS_ENABLED) {
        ContextMenu.init()
      }

      this._processPageContent()
    })
  },

  /**
   * 订阅事件总线上的事件
   * @private
   */
  _subscribeToEvents() {
    // 直接在 MainController 中订阅 TTS 开关事件，确保无论如何都能正确处理
    EventBus.subscribe('tts:toggle', (enabled) => {
      if (enabled) {
        ContextMenu.init()
      }
      else {
        ContextMenu.destroy()
      }
    })
  },

  /**
   * 编排所有对页面主要内容的处理流程
   * 此方法采用异步分批处理（Asynchronous Batch Processing）的策略
   * 以避免在处理包含大量文章的长页面时，因脚本长时间占用主线程而导致的页面卡顿或无响应
   * @private
   */
  _processPageContent() {
    const articleBodies = document.querySelectorAll('.article-body-inner')
    if (articleBodies.length === 0) {
      // 即使没有文章，也需要执行最终布局，以显示侧边栏
      PageOptimizer.finalizeLayout()
      return
    }

    let currentIndex = 0

    /**
     * 编排所有对页面主要内容的处理流程
     * 此方法采用异步分批处理（Asynchronous Batch Processing）的策略
     * 以避免在处理包含大量文章的长页面时，因脚本长时间占用主线程而导致的页面卡顿或无响应
     */
    const processBatch = () => {
      // 定义批次大小，每次处理 2 篇文章
      // Math.min 确保最后一批不会超出数组范围
      const batchSize = Math.min(2, articleBodies.length - currentIndex)
      const endIndex = currentIndex + batchSize

      // 在当前帧内，同步处理本批次的所有文章
      for (let i = currentIndex; i < endIndex; i++) {
        const body = articleBodies[i]
        // 1. 最先处理，确保在最原始的 DOM 上工作
        RubyConverter.applyRubyToContainer(body)
        // 2. 替换iframe为占位符
        IframeLoader.replaceIframesInContainer(body)
        // 3. 替换图片链接
        ImageProcessor.process(body)
        // 4. 最后做收尾清理和显示
        PageOptimizer.cleanupArticleBody(body)
      }
      // 将索引移动到下一批次的起始位置
      currentIndex = endIndex
      // 检查是否还有未处理的文章
      if (currentIndex < articleBodies.length) {
        // 使用 requestAnimationFrame 请求浏览器在下一次重绘前调用 processBatch
        requestAnimationFrame(processBatch)
      }
      else {
        // 所有批次处理完成，执行最终的全局布局调整
        PageOptimizer.finalizeLayout()
      }
    }
    // 启动第一个批次的处理
    requestAnimationFrame(processBatch)
  },
}

export default MainController
