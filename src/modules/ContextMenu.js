/**
 * @module ContextMenu
 * @description 管理划词出现的 TTS 快捷菜单
 * 当用户在文章中选择文本时，会显示一个朗读按钮，点击后使用 TTSPlayer 朗读选中内容
 */
import EventBus from '../utils/EventBus'

const ContextMenu = {
  _config: {
    MODULE_ENABLED: true,
    MENU_ID: 'selection-context-menu',
    MENU_OFFSET: 8, // 图标相对于鼠标指针的偏移量
    VALID_SELECTION_AREA: '.article-body-inner', // 可配置的有效选择区域
    MIN_SELECTION_LENGTH: 1, // 最小选择长度
    DRAG_THRESHOLD: 5, // 拖拽阈值，避免微小移动触发拖拽
    EMOJI_REGEX: /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu,
    STYLES: `
      #selection-context-menu { position: absolute; top: 0; left: 0; display: none; z-index: 9999; opacity: 0; user-select: none; will-change: transform, opacity; pointer-events: none; }
      #selection-context-menu.visible { opacity: 0.9; pointer-events: auto; transition: opacity 0.1s ease-out, transform 0.1s ease-out; }
      #selection-context-menu button { display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0; border-radius: 50%; cursor: pointer; border: none; background-color: #3B82F6; color: #FFFFFF; box-shadow: 0 5px 15px rgba(0,0,0,0.15), 0 2px 5px rgba(0,0,0,0.1); transition: background-color 0.2s ease-in-out, transform 0.2s ease-in-out; outline: none; }
      #selection-context-menu button:hover { background-color: #4B90F8; transform: scale(1.05); box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3); }
      #selection-context-menu button.is-dragging { cursor: grabbing; transform: scale(1); opacity: 0.7; transition: none; }
      #selection-context-menu button svg { width: 20px; height: 20px; stroke: currentColor; stroke-width: 2; pointer-events: none; }
    `,
  },

  // DOM 元素和状态
  menuElement: null,
  isDragging: false,
  dragUpdatePending: false,
  position: { x: 0, y: 0 },
  dragOffset: { x: 0, y: 0 },
  dragStart: { x: 0, y: 0 },
  abortController: null,
  boundHandlers: {
    dragMove: null,
    dragEnd: null,
  },

  /**
   * 初始化模块
   * @param {object} options - 配置选项
   */
  init(options = {}) {
    if (!this._config.MODULE_ENABLED)
      return
    if (this.menuElement)
      return // 避免重复初始化

    // 合并配置
    Object.assign(this._config, options)

    // 注入样式
    GM_addStyle(this._config.STYLES)

    // 创建菜单
    this._createMenu()

    // 绑定事件
    this._bindEvents()

    console.log('[INFO] [ContextMenu] 菜单已初始化')
  },

  /**
   * 销毁模块，清理资源
   */
  destroy() {
    if (!this.menuElement)
      return

    // 1. 清理由 AbortController 管理的静态事件
    this.abortController?.abort()

    // 2. 确保清理动态添加的拖拽事件
    if (this.boundHandlers.dragMove)
      document.removeEventListener('mousemove', this.boundHandlers.dragMove)
    if (this.boundHandlers.dragEnd)
      document.removeEventListener('mouseup', this.boundHandlers.dragEnd)

    // 3. 移除 DOM 元素
    this.menuElement.remove()

    // 4. 重置所有状态
    this._resetState()

    console.log('[INFO] [ContextMenu] 菜单已销毁')
  },

  /**
   * 重置内部状态
   * @private
   */
  _resetState() {
    this.menuElement = null
    this.isDragging = false
    this.dragUpdatePending = false
    this.position = { x: 0, y: 0 }
    this.dragOffset = { x: 0, y: 0 }
    this.dragStart = { x: 0, y: 0 }
    this.abortController = null
    this.boundHandlers = {
      dragMove: null,
      dragEnd: null,
    }
  },

  /**
   * 绑定事件处理器
   * @private
   */
  _bindEvents() {
    this.abortController = new AbortController()
    const { signal } = this.abortController

    // 这些是"静态"事件，在组件生命周期内一直存在
    this.menuElement.addEventListener('mousedown', this._handleDragStart.bind(this), { signal })
    document.addEventListener('mousedown', this._handleMouseDown.bind(this), { signal })
    document.addEventListener('mouseup', this._handleMouseUp.bind(this), { signal })
    document.addEventListener('keydown', this._handleKeyDown.bind(this), { signal })

    // 为拖拽事件预先绑定 this，以便能正确移除
    this.boundHandlers.dragMove = this._handleDragMove.bind(this)
    this.boundHandlers.dragEnd = this._handleDragEnd.bind(this)
  },

  /**
   * 创建菜单元素
   * @private
   */
  _createMenu() {
    if (document.getElementById(this._config.MENU_ID))
      return

    this.menuElement = document.createElement('div')
    this.menuElement.id = this._config.MENU_ID

    const readButton = document.createElement('button')
    readButton.title = 'Read'
    readButton.setAttribute('aria-label', 'Read selected text')
    readButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path></svg>`

    readButton.addEventListener('click', (event) => {
      if (this.isDragging) {
        event.stopPropagation()
        return
      }

      const cleanedText = this._getCleanedSelectedText()
      if (cleanedText) {
        // 使用 EventBus 发布事件，而不是直接调用 TTSPlayer
        EventBus.publish('tts:speak', cleanedText)
      }
    })

    this.menuElement.appendChild(readButton)
    document.body.appendChild(this.menuElement)
  },

  /**
   * 获取并清理选中的文本
   * @returns {string} 清理后的文本
   * @private
   */
  _getCleanedSelectedText() {
    try {
      const selection = window.getSelection()
      if (!selection || selection.rangeCount === 0)
        return ''

      const range = selection.getRangeAt(0)
      const fragment = range.cloneContents()
      const tempDiv = document.createElement('div')
      tempDiv.appendChild(fragment)

      // 清理操作
      const cleanupTasks = [
        () => tempDiv.querySelectorAll('rt').forEach(el => el.remove()), // 移除注音
        () => tempDiv.querySelectorAll('br').forEach(el => el.replaceWith('。')), // 替换换行
      ]
      cleanupTasks.forEach(task => task())

      // 获取并清理文本
      let text = tempDiv.textContent || ''
      text = text.replace(this._config.EMOJI_REGEX, '。') // 替换 Emoji
      text = text.replace(/\s+/g, '') // 清理空白

      return text
    }
    catch (error) {
      console.error('[ERROR] [ContextMenu] 清理选中文本时出错:', error)
      return ''
    }
  },

  /**
   * 处理拖拽开始事件
   * @param {MouseEvent} event - 鼠标事件
   * @private
   */
  _handleDragStart(event) {
    event.preventDefault()
    event.stopPropagation()

    this.isDragging = false
    this.dragStart = { x: event.pageX, y: event.pageY }
    this.dragOffset = { x: event.pageX - this.position.x, y: event.pageY - this.position.y }

    this.menuElement.style.transition = 'none' // 拖拽时禁用动画

    // 使用预先绑定的引用来添加监听器
    document.addEventListener('mousemove', this.boundHandlers.dragMove)
    document.addEventListener('mouseup', this.boundHandlers.dragEnd, { once: true })
  },

  /**
   * 处理拖拽移动事件
   * @param {MouseEvent} event - 鼠标事件
   * @private
   */
  _handleDragMove(event) {
    event.preventDefault()

    if (this.dragUpdatePending)
      return
    this.dragUpdatePending = true

    // 仅当未开始拖拽时，才检查阈值
    if (!this.isDragging) {
      const dx = event.pageX - this.dragStart.x
      const dy = event.pageY - this.dragStart.y
      if (Math.sqrt(dx * dx + dy * dy) > this._config.DRAG_THRESHOLD) {
        this.isDragging = true
        this.menuElement.querySelector('button')?.classList.add('is-dragging')
      }
    }

    requestAnimationFrame(() => {
      // 仅当拖拽状态为真时，才更新位置
      if (this.isDragging) {
        this.position.x = event.pageX - this.dragOffset.x
        this.position.y = event.pageY - this.dragOffset.y
        this.menuElement.style.transform = `translate(${this.position.x}px, ${this.position.y}px)`
      }
      this.dragUpdatePending = false
    })
  },

  /**
   * 处理拖拽结束事件
   * @private
   */
  _handleDragEnd() {
    // 使用相同的引用来移除监听器
    document.removeEventListener('mousemove', this.boundHandlers.dragMove)

    this.menuElement.querySelector('button')?.classList.remove('is-dragging')
    this.menuElement.style.transition = '' // 恢复动画

    setTimeout(() => {
      this.isDragging = false
    }, 0)
  },

  /**
   * 处理鼠标按下事件
   * @param {MouseEvent} event - 鼠标事件
   * @private
   */
  _handleMouseDown(event) {
    // 如果 mousedown 事件发生在菜单本身，则让拖拽处理器接管，不执行任何操作
    if (this.menuElement.contains(event.target)) {
      return
    }

    // 对于页面上任何其他地方的 mousedown，都意味着一次新交互的开始。
    // 立即隐藏菜单，以防止在开始新的文本选择时，旧的图标仍然残留。
    this._hideMenu()
  },

  /**
   * 处理鼠标松开事件
   * @param {MouseEvent} event - 鼠标事件
   * @private
   */
  _handleMouseUp(event) {
    // 忽略拖拽或点击菜单自身的事件
    if (this.isDragging || this.menuElement.contains(event.target)) {
      return
    }

    // 使用 setTimeout 推迟执行，以确保浏览器有足够时间完成选区状态的更新。
    // 这是必要的，因为 mouseup 事件触发时，selection 对象可能尚未最终确定。
    setTimeout(() => {
      const selection = window.getSelection()
      const selectedText = selection.toString().trim()
      const clickTargetIsValid = event.target.closest(this._config.VALID_SELECTION_AREA)

      // 核心决策：当且仅当用户在有效区域内完成了一次有效的文本选择时，才显示菜单。
      if (clickTargetIsValid && selectedText.length >= this._config.MIN_SELECTION_LENGTH) {
        this._showMenu(event.pageX, event.pageY)
      }
      else {
        // 在所有其他情况下，隐藏菜单并清除选区，确保行为一致。
        this._hideMenu()
        selection?.removeAllRanges()
      }
    }, 0)
  },

  /**
   * 处理键盘事件
   * @param {KeyboardEvent} event - 键盘事件
   * @private
   */
  _handleKeyDown(event) {
    if (event.key === 'Escape' && this.menuElement && this.menuElement.classList.contains('visible')) {
      this._hideMenu()
      window.getSelection()?.removeAllRanges()
    }
  },

  /**
   * 显示菜单
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @private
   */
  _showMenu(x, y) {
    if (!this.menuElement)
      return

    this.position.x = x + this._config.MENU_OFFSET
    this.position.y = y + this._config.MENU_OFFSET

    this.menuElement.style.transition = 'none'
    this.menuElement.style.transform = `translate(${this.position.x}px, ${this.position.y}px)`
    this.menuElement.style.display = 'block'

    requestAnimationFrame(() => {
      this.menuElement.style.transition = ''
      this.menuElement.classList.add('visible')
    })
  },

  /**
   * 隐藏菜单
   * @private
   */
  _hideMenu() {
    if (!this.menuElement || !this.menuElement.classList.contains('visible'))
      return

    // 移除 .visible 类，由于 transition 只在 .visible 上定义，所以隐藏是瞬时的
    this.menuElement.classList.remove('visible')

    // 立即设置 display: none，因为隐藏是瞬时的，不需要等待 transitionend
    this.menuElement.style.display = 'none'
  },
}

export default ContextMenu
