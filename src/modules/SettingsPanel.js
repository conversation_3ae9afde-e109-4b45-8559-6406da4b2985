/**
 * @module SettingsPanel
 * @description 管理设置面板的创建、事件处理和状态持久化
 * 此模块负责在页面上创建一个浮动设置面板，允许用户动态地开关脚本功能和调整振假名 (Furigana) 的可见性
 * 所有设置都会通过 GM_setValue/GM_getValue 进行持久化保存
 */
import EventBus from '../utils/EventBus'

const SettingsPanel = {
  _config: {
    MODULE_ENABLED: true, // 是否启用设置面板
    FEEDBACK_URL: 'https://greasyfork.org/scripts/542386-edewakaru-enhanced',
    OPTIONS: {
      SCRIPT_ENABLED: { label: 'ページ最適化', defaultValue: true, handler: '_handleScriptToggle', isChild: false },
      FURIGANA_VISIBLE: { label: '振り仮名表示', defaultValue: true, handler: '_handleFuriganaToggle', isChild: true },
      IFRAME_LOAD_ENABLED: { label: '関連記事表示', defaultValue: true, handler: '_handleIframeLoadToggle', isChild: true },
      TTS_ENABLED: { label: '単語選択発音', defaultValue: false, handler: '_handleTtsToggle', isChild: true },
    },
    STYLES: `
      #settings-panel { position: fixed; bottom: 24px; right: 24px; z-index: 9999; display: flex; flex-direction: column; gap: 8px; padding: 16px; background: white; border-radius: 4px; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); width: 140px; opacity: 0.9; -webkit-user-select: none; user-select: none; }
      .settings-title { font-size: 14px; font-weight: 600; color: #1F2937; margin: 0 0 6px 0; text-align: center; border-bottom: 1px solid #E5E7EB; padding-bottom: 6px; position: relative; }
      .feedback-link, .feedback-link:visited { position: absolute; top: 0; right: 0; width: 16px; height: 16px; color: #E5E7EB !important; transition: color 0.2s ease-in-out; }
      .feedback-link:hover { color: #3B82F6 !important; }
      .feedback-link svg { width: 100%; height: 100%; }
      .setting-item { display: flex; align-items: center; justify-content: space-between; gap: 8px; }
      .setting-label { font-size: 13px; font-weight: 500; color: #4B5563; cursor: pointer; flex: 1; line-height: 1.2; }
      .toggle-switch { position: relative; display: inline-block; width: 40px; height: 20px; flex-shrink: 0; }
      .toggle-switch.disabled { opacity: 0.5; pointer-events: none; }
      .toggle-switch input { opacity: 0; width: 0; height: 0; }
      .toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #E5E7EB; transition: all 0.2s ease-in-out; border-radius: 9999px; }
      .toggle-slider:before { position: absolute; content: ""; height: 15px; width: 15px; left: 2.5px; bottom: 2.5px; background-color: white; transition: all 0.2s ease-in-out; border-radius: 50%; box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1),0 1px 2px 0 rgba(0,0,0,0.06); }
      input:checked+.toggle-slider { background-color: #3B82F6; }
      input:checked+.toggle-slider:before { transform: translateX(20px); }
      .settings-notification { position: fixed; bottom: 208px; right: 24px; z-index: 9999; padding: 8px 12px; background-color: #3B82F6; color: white; border-radius: 6px; font-size: 13px; animation: slideInOut 3s ease-in-out; -webkit-user-select: none; user-select: none; }
      @keyframes slideInOut { 0%, 100% { opacity: 0; transform: translateX(20px); } 15%, 85% { opacity: 0.9; transform: translateX(0); } }
    `,
  },

  // 用于缓存 UI 元素，避免重复查询 DOM
  _uiElements: {},

  /**
   * 初始化设置面板
   */
  init() {
    if (!this._config.MODULE_ENABLED)
      return

    // 注入样式
    GM_addStyle(this._config.STYLES)

    // 创建面板
    this._createPanel()

    // 初始化振假名显示状态
    this._initializeFuriganaDisplay()
  },

  /**
   * 从存储中获取所有设置的原始值。
   * @returns {object} - 一个包含所有选项当前值的对象，键名保持全大写。
   */
  getOptions() {
    const options = {}
    for (const key in this._config.OPTIONS) {
      options[key] = GM_getValue(key, this._config.OPTIONS[key].defaultValue)
    }
    return options
  },

  /**
   * 事件处理器：处理主开关切换
   * @param {boolean} enabled - 开关状态
   * @private
   */
  _handleScriptToggle(enabled) {
    GM_setValue('SCRIPT_ENABLED', enabled)
    this._showNotification()
    this._updateChildOptionsUI(enabled)
  },

  /**
   * 事件处理器：处理振假名可见性切换
   * @param {boolean} visible - 可见性状态
   * @private
   */
  _handleFuriganaToggle(visible) {
    GM_setValue('FURIGANA_VISIBLE', visible)
    this._toggleFuriganaDisplay(visible)
  },

  /**
   * 事件处理器：处理 iframe 加载切换
   * @param {boolean} enabled - 开关状态
   * @private
   */
  _handleIframeLoadToggle(enabled) {
    GM_setValue('IFRAME_LOAD_ENABLED', enabled)
    this._showNotification()
  },

  /**
   * 事件处理器：处理 TTS 开关切换
   * @param {boolean} enabled - 开关状态
   * @private
   */
  _handleTtsToggle(enabled) {
    GM_setValue('TTS_ENABLED', enabled)
    // 使用 EventBus 发布事件，而不是直接导入和调用 ContextMenu
    EventBus.publish('tts:toggle', enabled)
  },

  /**
   * 切换振假名显示状态
   * @param {boolean} visible - 是否可见
   * @private
   */
  _toggleFuriganaDisplay(visible) {
    const id = 'furigana-display-style'
    let style = document.getElementById(id)

    if (!style) {
      style = document.createElement('style')
      style.id = id
      document.head.appendChild(style)
    }

    style.textContent = `rt { display: ${visible ? 'ruby-text' : 'none'} !important; }`
  },

  /**
   * 根据保存的设置初始化振假名显示状态
   * @private
   */
  _initializeFuriganaDisplay() {
    // 因为注音默认就是可见的，不需要处理
    if (!GM_getValue('FURIGANA_VISIBLE', this._config.OPTIONS.FURIGANA_VISIBLE.defaultValue)) {
      this._toggleFuriganaDisplay(false)
    }
  },

  /**
   * 创建设置面板
   * @private
   */
  _createPanel() {
    if (!this._config.MODULE_ENABLED)
      return

    const panel = document.createElement('div')
    panel.id = 'settings-panel'
    panel.innerHTML = `
      <h3 class="settings-title">
        設定パネル
        <a href="${this._config.FEEDBACK_URL}" target="_blank" rel="noopener noreferrer" class="feedback-link" title="Feedback">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
        </a>
      </h3>
    `

    const isMasterEnabled = GM_getValue('SCRIPT_ENABLED', this._config.OPTIONS.SCRIPT_ENABLED.defaultValue)

    // 直接遍历 _config.OPTIONS 来创建所有 UI 开关
    for (const key in this._config.OPTIONS) {
      const config = this._config.OPTIONS[key]
      let isDisabled = config.isChild && !isMasterEnabled

      if (key === 'TTS_ENABLED' && !('speechSynthesis' in window)) {
        isDisabled = true
      }

      panel.appendChild(this._createToggle(key, config, isDisabled))
    }

    document.body.appendChild(panel)
  },

  /**
   * 创建单个开关控件
   * @param {string} key - 设置键名
   * @param {object} config - 设置配置
   * @param {boolean} isDisabled - 是否禁用
   * @returns {HTMLElement} 创建的开关元素
   * @private
   */
  _createToggle(key, config, isDisabled) {
    const { label, handler, defaultValue } = config
    const id = `setting-${key.toLowerCase()}`
    const itemContainer = document.createElement('div')
    itemContainer.className = 'setting-item'
    itemContainer.dataset.key = key

    const isChecked = GM_getValue(key, defaultValue)
    itemContainer.innerHTML = `
      <label for="${id}" class="setting-label">${label}</label>
      <label class="toggle-switch ${isDisabled ? 'disabled' : ''}">
        <input type="checkbox" id="${id}" ${isChecked ? 'checked' : ''}>
        <span class="toggle-slider"></span>
      </label>
    `

    const toggleSwitch = itemContainer.querySelector('.toggle-switch')
    this._uiElements[key] = { switch: toggleSwitch }
    itemContainer.querySelector('input').addEventListener('change', e => this[handler](e.target.checked))

    return itemContainer
  },

  /**
   * 更新子选项 UI 状态
   * @param {boolean} masterEnabled - 主开关状态
   * @private
   */
  _updateChildOptionsUI(masterEnabled) {
    for (const key in this._config.OPTIONS) {
      if (this._config.OPTIONS[key].isChild) {
        const uiElement = this._uiElements[key]
        if (uiElement && uiElement.switch) {
          uiElement.switch.classList.toggle('disabled', !masterEnabled)
        }
      }
    }
  },

  /**
   * 显示通知
   * @param {string} message - 通知消息
   * @private
   */
  _showNotification(message = '設定を保存しました。再読み込みしてください。') {
    const el = document.createElement('div')
    el.className = 'settings-notification'
    el.textContent = message
    document.body.appendChild(el)
    setTimeout(() => el.remove(), 3000)
  },
}

export default SettingsPanel
