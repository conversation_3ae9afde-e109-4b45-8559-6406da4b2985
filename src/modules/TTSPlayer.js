/**
 * @module TTSPlayer
 * @description 文本朗读功能模块，利用浏览器的 SpeechSynthesis API 实现日语朗读
 */
import EventBus from '../utils/EventBus'

const TTSPlayer = {
  _config: {
    VOICE_NAMES: ['Microsoft Nanami Online (Natural) - Japanese (Japan)', 'Microsoft Keita Online (Natural) - Japanese (Japan)'],
    LANG: 'ja-JP',
  },

  _initPromise: null,
  _voices: [],
  _unsubscribeHandlers: [],

  /**
   * 初始化 TTSPlayer 模块
   */
  init() {
    // 初始化语音合成引擎
    if (!this._initPromise) {
      this._initPromise = this._initialize()
    }

    // 订阅事件
    this._subscribeToEvents()

    return this._initPromise
  },

  /**
   * 清理资源
   */
  destroy() {
    // 取消所有事件订阅
    this._unsubscribeHandlers.forEach(unsubscribe => unsubscribe())
    this._unsubscribeHandlers = []

    // 取消当前正在播放的语音
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel()
    }
  },

  /**
   * 订阅事件总线上的事件
   * @private
   */
  _subscribeToEvents() {
    // 订阅 TTS 朗读事件
    const unsubscribeTtsSpeak = EventBus.subscribe('tts:speak', (text) => {
      this.speak(text)
    })

    this._unsubscribeHandlers.push(unsubscribeTtsSpeak)
  },

  /**
   * 朗读文本
   * @param {string} text - 要朗读的文本
   * @returns {Promise<void>}
   */
  async speak(text) {
    if (!this._initPromise) {
      this._initPromise = this._initialize()
    }

    await this._initPromise

    // 取消当前正在播放的语音
    speechSynthesis.cancel()

    if (!text?.trim() || this._voices.length === 0) {
      this._voices.length === 0 && console.warn('[WARN] [TTSPlayer] TTS 功能不可用，无法播放。')
      return
    }

    const utterance = new SpeechSynthesisUtterance(text)

    // 随机选择一个语音
    utterance.voice = this._voices[Math.floor(Math.random() * this._voices.length)]
    utterance.lang = this._config.LANG

    // 错误处理
    utterance.onerror = (e) => {
      !['canceled', 'interrupted'].includes(e.error) && console.error(`[ERROR] [TTSPlayer] TTS 播放错误: ${e.error}`)
    }

    // 开始朗读
    speechSynthesis.speak(utterance)
  },

  /**
   * 初始化 TTS 引擎，加载可用语音
   * @returns {Promise<void>}
   * @private
   */
  _initialize() {
    return new Promise((resolve) => {
      if (!('speechSynthesis' in window)) {
        console.warn('[WARN] [TTSPlayer] 浏览器不支持语音合成 (SpeechSynthesis)。')
        return resolve()
      }

      console.log('[INFO] [TTSPlayer] 正在初始化并加载语音列表...')
      let resolved = false

      const loadVoices = () => {
        if (resolved)
          return
        resolved = true

        const allVoices = speechSynthesis.getVoices()
        const { VOICE_NAMES, LANG } = this._config

        // 过滤出符合条件的日语语音
        this._voices = allVoices.filter(v => VOICE_NAMES.includes(v.name) && v.lang === LANG)

        if (this._voices.length > 0) {
          console.log(`[INFO] [TTSPlayer] 初始化成功，找到 ${this._voices.length} 个可用日语音色。`)
        }
        else {
          console.warn('[WARN] [TTSPlayer] 未找到指定的日语音色，TTS 功能可能不可用。')
        }

        // 清理事件监听器
        speechSynthesis.onvoiceschanged = null
        resolve()
      }

      // 三重保险机制
      const initialVoices = speechSynthesis.getVoices()
      if (initialVoices.length > 0) {
        loadVoices()
      }
      else {
        speechSynthesis.onvoiceschanged = loadVoices
        setTimeout(loadVoices, 500) // 兜底超时
      }
    })
  },
}

export default TTSPlayer
