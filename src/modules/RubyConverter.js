/**
 * @module RubyConverter
 * @description 封装所有关于注音转换的逻辑，包括词库、规则和 DOM 处理
 * 这是脚本的核心功能模块，它遵循一个明确的优先级顺序来转换文本：
 * 优先级 0: HTML 级别强制替换 (HTML)
 * 优先级 1: 手动指定的特例词汇 (TEXT.MANUAL_MARK, TEXT.OVERRIDE_READING, TEXT.FULL_REPLACE)
 * 优先级 2: bracket 动态学习的词汇 (从【...】或「...」中提取)
 * 优先级 3: katakana片假名(英文)模式
 * 优先级 4: ruby 通用汉字(注音)模式，并应用各种排除规则
 */

const RubyConverter = {
  // ----------------------------------------------------------------
  // § 1. 配置与核心属性 (Configuration & Core Properties)
  // ----------------------------------------------------------------
  _config: {
    MODULE_ENABLED: true,
  },

  // 规则配置
  _rules: null,

  // 正则表达式集合
  _regex: {
    // 匹配模式 - match 开头表示需要提取内容的匹配
    matchBracketRuby: /[【「]([^【】「」（）・、\s～〜]+)（([^（）]*)）([^【】「」（）]*)[】」]/g,
    matchKanjiRuby: /([一-龠々ヵヶ]+)\s*[(（]([^（）()]*)[)）]/g,
    matchLoanwordRuby: /([a-z]+(?:\s+[a-z]+)*)\s*[（(]([ァ-ンー]+)[）)]/gi,
    matchKatakanaRuby: /([ァ-ンー]+)\s*[（(]([a-z+＋\s]+)[）)]/gi,

    // 提取模式 - extract 开头表示用于提取规则的正则
    extractCandidates: /([^\s\p{P}<>]+)（([^）]+)）/gu,
    extractKanjiAndReading: /(.*?)（(.*?)）/,
    extractReading: /（(.*?)）/,

    // 检查模式 - test 开头表示用于验证的正则
    testNonKana: /[^\u3040-\u309F\u30A0-\u30FF]/,
    testHtmlTag: /<[^>]+>/g,
    testRubyTag: /<ruby[^>]*>/,
  },

  // --- 以下为"词库引擎"的核心数据结构 ---
  // 已注册词条的 Set，用于高效去重
  _registeredWords: new Set(),

  // 用于构建最终 globalRegex 的词条模式源
  _wordBankForRegex: [],

  // 预处理好的 Ruby 结果缓存，我们的"最终词库"
  _rubyCache: new Map(),

  // 预编译好的 HTML 补丁集，用于修复特殊 HTML 结构
  _htmlPatches: new Map(),

  // 简单文本替换映射，用于第一阶段的纯文本修正
  _simpleTextReplacements: new Map(),

  // 最终生成的"超级搜索引擎"正则
  globalRegex: null,

  // ----------------------------------------------------------------
  // § 2. 公共接口方法 (Public API Methods)
  // ----------------------------------------------------------------

  /**
   * 初始化引擎，这是外部调用的主入口。
   * 它将触发对所有静态规则的编译。
   * @param {object} rules - 原始的 RULES 对象
   */
  init(rules) {
    if (!this._config.MODULE_ENABLED)
      return
    this._rules = rules
    console.log('[INFO] FuranEngine: 正在编译静态规则...')
    this.compile()
    console.log(`[INFO] FuranEngine: 编译完成。已注册 ${this._registeredWords.size} 个词条，已创建 ${this._htmlPatches.size} 个 HTML 补丁。`)
  },

  /**
   * 处理一个具体的页面DOM容器。
   * 这是第二阶段"页面处理"的入口。
   * 为了保持外部调用兼容性，我们保留 applyRubyToContainer 这个名称。
   * @param {HTMLElement} container - 需要处理的DOM元素
   */
  applyRubyToContainer(container) {
    if (!this._config.MODULE_ENABLED)
      return

    // 步骤 2.1: 应用HTML补丁
    this._applyHtmlPatches(container)

    // 步骤 2.2: 从容器中学习新词
    this._learnFromContainer(container)

    // 步骤 2.3: 整合所有规则，生成最终的正则
    this._buildFinalRegex()

    // 步骤 2.4: 对所有文本节点应用替换链
    this._processTextNodes(container)
  },

  // ----------------------------------------------------------------
  // § 3. 内部核心方法 (Internal Core Methods)
  // ----------------------------------------------------------------

  /**
   * 阶段一：编译。
   * 此阶段处理所有与页面内容无关的静态规则。
   */
  compile() {
    this._compileStaticRules()
    this._buildFinalRegex() // 根据静态规则先构建一次
  },

  /**
   * 编译所有来自 _rawConfig 的静态规则。
   * 它会区分哪些规则可以被注册进"词库"，哪些是复杂的"HTML补丁"。
   */
  _compileStaticRules() {
    // --- HTML 规则处理 ---
    const { HTML, TEXT } = this._rules

    // 1. 处理 HTML.FULL_REPLACE -> 它们总是 HTML 补丁
    HTML.FULL_REPLACE.forEach((rule) => {
      // 步骤 1: 创建 HTML 补丁（所有 HTML.FULL_REPLACE 都需要创建补丁）
      const pattern = typeof rule.pattern === 'string' ? new RegExp(this._escapeRegExp(rule.pattern), 'g') : rule.pattern
      this._htmlPatches.set(pattern, rule.replacement)

      // 步骤 2: 根据 replacement 是否包含 <ruby> 标签决定是否提取额外词条
      try {
        if (this._regex.testRubyTag.test(rule.replacement)) {
          // 有 <ruby> 标签的不需要额外提取
          console.log(`[INFO] [HTML.FULL_REPLACE] 跳过提取: "${rule.pattern}" (包含 <ruby> 标签)`)
        }
        else {
          // 没有 <ruby> 标签的需要从 replacement 中提取词条
          const extractedPairs = this._extractRubyCandidates(rule.replacement)
          extractedPairs.forEach(({ kanji, reading, fullPattern }) => {
            this.registerWord({
              pattern: fullPattern,
              kanji,
              reading,
              source: 'HTML.FULL_REPLACE.提取',
            })
          })
          console.log(`[INFO] [HTML.FULL_REPLACE] 处理 "${rule.pattern}": 提取了 ${extractedPairs.length} 个词条`)
        }
      }
      catch (e) {
        console.error(`[ERROR] [HTML.FULL_REPLACE] 处理规则时发生错误: "${rule.pattern}"`, e)
      }
    })

    // 2. 处理 HTML.MANUAL_MARK
    HTML.MANUAL_MARK.forEach((patternString) => {
      // --- 分支 A：处理带 '?' 通配符的模式 ---
      if (patternString.includes('?')) {
        const [prefix, suffix] = patternString.split('?')
        if (suffix === undefined)
          return

        // a. 准备用于注册到词库的纯净数据
        const cleanPattern = prefix + suffix
        const readingMatchForRegister = cleanPattern.match(this._regex.extractReading)
        if (readingMatchForRegister) {
          const kanjiPartForRegister = cleanPattern.replace(this._regex.extractReading, '')
          const readingForRegister = readingMatchForRegister[1]

          // 将纯净版本注册到文本词库，以备后续在纯文本节点中使用
          this.registerWord({
            pattern: cleanPattern,
            kanji: kanjiPartForRegister,
            reading: readingForRegister,
            source: 'HTML.MANUAL_MARK',
          })
        }

        // b. 创建 HTML 补丁，用于处理 innerHTML
        const searchPattern = new RegExp(`${this._escapeRegExp(prefix)}((?:<[^>]+>)*?)${this._escapeRegExp(suffix)}`, 'g')
        let replacementFunc
        const readingForPatch = readingMatchForRegister ? readingMatchForRegister[1] : ''

        if (!this._regex.testNonKana.test(readingForPatch)) {
          const textPartForPatch = cleanPattern.replace(this._regex.extractReading, '')
          const rubyHtml = this._parseFurigana(textPartForPatch, readingForPatch)
          if (rubyHtml !== null) {
            replacementFunc = (match, capturedTags) => rubyHtml + (capturedTags || '')
          }
          else {
            replacementFunc = match => match
          }
        }
        else {
          const textOnlyForPatch = cleanPattern
          replacementFunc = (match, capturedTags) => textOnlyForPatch + (capturedTags || '')
        }
        this._htmlPatches.set(searchPattern, replacementFunc)
      }
      else {
        // --- 分支 B：处理不带 '?' 的旧模式 --- 暂不处理
      }
    })

    // --- TEXT 规则处理 ---
    // 1. 处理 TEXT.MANUAL_MARK
    TEXT.MANUAL_MARK.forEach((patternString) => {
      const match = patternString.match(this._regex.extractKanjiAndReading)
      if (match) {
        this.registerWord({
          pattern: patternString,
          kanji: match[1],
          reading: match[2],
          source: 'TEXT.MANUAL_MARK',
        })
      }
    })

    // 2. 处理 TEXT.FULL_REPLACE
    TEXT.FULL_REPLACE.forEach((rule) => {
      try {
        // 根据 replacement 是否包含 <ruby> 标签决定处理方式
        if (this._regex.testRubyTag.test(rule.replacement)) {
          // 含 <ruby> 的规则：直接注册为最终词汇，参与全局正则匹配
          this.registerWord({
            pattern: rule.pattern,
            replacement: rule.replacement,
            source: 'TEXT.FULL_REPLACE.最终词汇',
          })
        }
        else {
          // 纯文本修正规则：存放到简单替换映射中
          this._simpleTextReplacements.set(this._escapeRegExp(rule.pattern), rule.replacement)

          // 同时尝试从 replacement 中提取词条（如果有的话）
          const extractedPairs = this._extractRubyCandidates(rule.replacement)
          extractedPairs.forEach(({ kanji, reading, fullPattern }) => {
            this.registerWord({
              pattern: fullPattern,
              kanji,
              reading,
              source: 'TEXT.FULL_REPLACE.提取',
            })
          })
          console.log(`[INFO] [TEXT.FULL_REPLACE] 处理 "${rule.pattern}": 提取了 ${extractedPairs.length} 个词条`)
        }
      }
      catch (e) {
        console.error(`[ERROR] [TEXT.FULL_REPLACE] 处理规则时发生错误: "${rule.pattern}"`, e)
      }
    })
  },

  /**
   * 统一的、权威的"词条注册"方法。
   * @param {object} options - 注册选项
   * @param {string} options.pattern - 格式为 `单词（读音）` 的字符串
   * @param {string} options.kanji - 汉字部分
   * @param {string} options.reading - 读音部分
   * @param {string} options.source - 规则来源，用于日志输出
   * @param {string} [options.replacement] - 可选的、直接指定的替换结果
   */
  registerWord({ pattern, kanji, reading, replacement, source }) {
    // 步骤 1: 去重检查
    if (this._registeredWords.has(pattern)) {
      return // 已注册，直接返回
    }

    // 步骤 2: 提取核心信息
    let finalReplacement = replacement

    // 步骤 3: 生成替换结果
    if (!finalReplacement) {
      if (!kanji || typeof reading === 'undefined')
        return
      const rubyHtml = this._parseFurigana(kanji, reading)
      if (rubyHtml === null) {
        if (source === '动态学习') {
          console.warn(`[WARN] [核验失败] 来源: ${source}, 词条: "${pattern}" 将被跳过。`)
        }
        return
      }
      finalReplacement = rubyHtml
    }

    // 步骤 4: 存入词库
    this._registeredWords.add(pattern)
    this._wordBankForRegex.push(this._escapeRegExp(pattern))
    this._rubyCache.set(pattern, finalReplacement)

    // 步骤 5: 仅对动态学习的词条进行日志输出
    if (source === '动态学习') {
      console.log(`[INFO] [动态学习] 成功学习词条: "${pattern}" => "${finalReplacement}"`)
    }
  },

  /**
   * 从容器中学习新词，并注册它们。
   * @param {HTMLElement} container
   */
  _learnFromContainer(container) {
    const html = container.innerHTML
    const textOnly = html.replace(this._regex.testHtmlTag, '')
    let learnedCount = 0
    let skippedCount = 0

    for (const match of textOnly.matchAll(this._regex.matchBracketRuby)) {
      const kanjiPart = match[1]
      const readingPart = match[2]
      const corePattern = `${kanjiPart}（${readingPart}）`

      // 检查是否已在注册词库中
      if (this._registeredWords.has(corePattern)) {
        console.warn(`[WARN] [动态学习] 跳过已注册词条: "${corePattern}"`)
        skippedCount++
        continue
      }

      // 检查是否已在简单替换映射中
      if (this._simpleTextReplacements.has(this._escapeRegExp(corePattern))) {
        console.warn(`[WARN] [动态学习] 跳过已存在于替换列表的词条: "${corePattern}"`)
        skippedCount++
        continue
      }

      // 进行基本检查
      if (this._regex.testNonKana.test(readingPart)) {
        console.warn(`[WARN] [动态学习] 跳过非假名读音: "${corePattern}"`)
        skippedCount++
        continue
      }

      if (this._hasInvalidChars(kanjiPart)) {
        console.warn(`[WARN] [动态学习] 跳过包含无效字符的汉字: "${corePattern}"`)
        skippedCount++
        continue
      }

      this.registerWord({
        pattern: corePattern,
        kanji: kanjiPart,
        reading: readingPart,
        source: '动态学习',
      })
      learnedCount++
    }

    if (learnedCount > 0 || skippedCount > 0) {
      //  console.log(`[INFO] [动态学习] 完成容器学习: 学习 ${learnedCount} 个词条, 跳过 ${skippedCount} 个无效词条`)
    }
  },

  /**
   * 应用 HTML 补丁来修复特殊结构。
   * @param {HTMLElement} container
   */
  _applyHtmlPatches(container) {
    if (this._htmlPatches.size === 0) {
      console.log(`[INFO] [HTML补丁] 无补丁需要应用`)
      return
    }

    let html = container.innerHTML
    const originalHtml = html
    let appliedCount = 0

    this._htmlPatches.forEach((replacement, pattern) => {
      const beforeReplace = html
      html = html.replace(pattern, replacement)
      if (html !== beforeReplace) {
        appliedCount++
      }
    })

    if (html !== originalHtml) {
      container.innerHTML = html
      console.log(`[INFO] [HTML补丁] 完成应用: 共应用 ${appliedCount} 个补丁`)
    }
    else {
      console.log(`[INFO] [HTML补丁] 无匹配内容需要修补`)
    }
  },

  /**
   * 根据当前的词库，构建或重建 globalRegex。
   */
  _buildFinalRegex() {
    this.globalRegex = this._wordBankForRegex.length > 0 ? new RegExp(`(${this._wordBankForRegex.join('|')})`, 'g') : null
  },

  /**
   * 遍历容器的所有文本节点，并应用替换链。
   * @param {HTMLElement} root
   */
  _processTextNodes(root) {
    const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
      acceptNode: n => (n.parentNode.nodeName !== 'SCRIPT' && n.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
    })

    const nodesToProcess = []
    let node = walker.nextNode()

    while (node) {
      const newContent = this._applyTextReplacements(node.nodeValue)
      if (newContent !== node.nodeValue) {
        nodesToProcess.push({ node, newContent })
      }
      node = walker.nextNode()
    }

    // 从后往前替换，避免影响 DOM 结构
    for (let i = nodesToProcess.length - 1; i >= 0; i--) {
      const { node, newContent } = nodesToProcess[i]
      const fragment = document.createRange().createContextualFragment(newContent)
      node.parentNode.replaceChild(fragment, node)
    }
  },

  /**
   * 对纯文本内容执行多步替换链
   * @param {string} text
   * @returns {string} 处理后的文本
   */
  _applyTextReplacements(text) {
    // 快速检查：如果文本中不包含括号，直接返回
    if (!text.includes('（') && !text.includes('(')) {
      return text
    }

    let processedText = text

    // 第一步：应用所有纯文本修正（使用 Map）
    for (const [pattern, replacement] of this._simpleTextReplacements) {
      const regex = new RegExp(pattern, 'g')
      processedText = processedText.replace(regex, replacement)
    }

    // 第二步：通过一次全局 replace 高效处理所有已注册的词条（包括从 FULL_REPLACE 中学到的词条）
    if (this.globalRegex) {
      processedText = processedText.replace(this.globalRegex, (match) => {
        return this._rubyCache.get(match) || match
      })
    }

    // 第三步：片假名模式即时匹配
    processedText = processedText.replace(this._regex.matchLoanwordRuby, (match, loanword, katakana) => {
      return `<ruby>${loanword}<rt>${katakana}</rt></ruby>`
    })
    processedText = processedText.replace(this._regex.matchKatakanaRuby, (match, katakana, loanword) => {
      return `<ruby>${katakana}<rt>${loanword}</rt></ruby>`
    })

    // 第四步：通用汉字模式后备匹配 (含排除检查)
    processedText = processedText.replace(this._regex.matchKanjiRuby, (match, kanji, reading) => {
      const fullMatch = `${kanji}（${reading}）`
      // 检查是否满足排除条件
      if (this._rules.EXCLUDE.STRINGS.has(fullMatch) || this._regex.testNonKana.test(reading) || this._rules.EXCLUDE.PARTICLES.has(reading)) {
        console.warn(`[WARN] 通用汉字模式排除: "${fullMatch}"`)
        return match // 满足排除条件，不转换
      }
      return `<ruby>${kanji}<rt>${reading}</rt></ruby>`
    })

    return processedText
  },

  // ----------------------------------------------------------------
  // § 4. 工具/辅助方法 (Utility/Helper Methods)
  // ----------------------------------------------------------------

  /**
   * 解析振假名，将汉字和读音转换为 ruby 标签
   * @param {string} kanji - 汉字部分
   * @param {string} reading - 读音部分
   * @returns {string|null} - 生成的 ruby HTML 或 null（如果解析失败）
   */
  _parseFurigana(kanji, reading) {
    // 检查 kanji 或 reading 是否全是片假名，如果任一全是片假名则跳过处理
    if (this._isAllKatakana(kanji) || this._isAllKatakana(reading)) {
      console.warn(`[WARN] [振假名解析] 跳过全片假名词条: "${kanji}（${reading}）"，由片假名专用规则处理`)
      return null // 跳过处理，由片假名专用规则处理
    }

    const hiraganaReading = this._katakanaToHiragana(reading)
    let result = ''
    let kanjiIndex = 0
    let readingIndex = 0

    while (kanjiIndex < kanji.length) {
      const currentKanjiChar = kanji[kanjiIndex]

      if (this._isKanaChar(currentKanjiChar)) {
        result += currentKanjiChar
        const hiraganaCurrent = this._katakanaToHiragana(currentKanjiChar)
        const tempNextReadingIndex = hiraganaReading.indexOf(hiraganaCurrent, readingIndex)

        if (tempNextReadingIndex !== -1) {
          readingIndex = tempNextReadingIndex + hiraganaCurrent.length
        }
        else {
          console.error(`[WARN] [振假名解析] 假名不匹配: "${kanji}（${reading}）"，详情 "${currentKanjiChar}" 在读音 "${hiraganaReading}" 中未找到`)
          return null // 严重错误：假名在读音中不匹配
        }

        kanjiIndex++
      }
      else {
        let kanjiPart = ''
        let blockEndIndex = kanjiIndex

        while (blockEndIndex < kanji.length && !this._isKanaChar(kanji[blockEndIndex])) {
          kanjiPart += kanji[blockEndIndex]
          blockEndIndex++
        }

        const nextKanaInKanji = kanji[blockEndIndex]
        let readingEndIndex

        if (nextKanaInKanji) {
          const hiraganaNextKana = this._katakanaToHiragana(nextKanaInKanji)
          readingEndIndex = hiraganaReading.indexOf(hiraganaNextKana, readingIndex)

          if (readingEndIndex === -1) {
            readingEndIndex = hiraganaReading.length
          }
        }
        else {
          readingEndIndex = hiraganaReading.length
        }

        const readingPart = reading.substring(readingIndex, readingEndIndex)

        if (kanjiPart) {
          if (!readingPart) {
            console.error(`[WARN] [振假名解析] 汉字无对应读音: "${kanji}（${reading}）"，详情 "${kanjiPart}" 在读音中无匹配部分`)
            return null // 严重错误：汉字部分没有对应的读音
          }
          else {
            result += `<ruby>${kanjiPart}<rt>${readingPart}</rt></ruby>`
          }
        }

        readingIndex = readingEndIndex
        kanjiIndex = blockEndIndex
      }
    }

    // 最后检查是否有多余的读音未被使用
    if (readingIndex < hiraganaReading.length) {
      console.warn(`[WARN] [振假名解析] 存在多余的读音字符: "${kanji}（${reading}）"，详情 "${kanji}" 存在多余读音 "${hiraganaReading.substring(readingIndex)}"`)
    }

    return result
  },

  /**
   * 转义正则表达式中的特殊字符
   * @param {string} string - 需要转义的字符串
   * @returns {string} 转义后的字符串
   * @private
   */
  _escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  },

  /**
   * 将片假名转换为平假名
   * @param {string} str - 包含片假名的字符串
   * @returns {string} 转换后的字符串
   * @private
   */
  _katakanaToHiragana(str) {
    if (!str)
      return ''
    return str.replace(/[\u30A1-\u30F6]/g, match => String.fromCharCode(match.charCodeAt(0) - 0x60))
  },

  /**
   * 检查字符是否为假名字符
   * @param {string} char - 要检查的单个字符
   * @returns {boolean} 是否为假名字符
   * @private
   */
  _isKanaChar(char) {
    return /^[\p{Script=Hiragana}\p{Script=Katakana}ー]$/u.test(char)
  },

  /**
   * 检查字符串是否包含非假名/汉字字符
   * @param {string} str - 要检查的字符串
   * @returns {boolean} 是否包含非假名/汉字字符
   * @private
   */
  _hasInvalidChars(str) {
    return /[^一-龠々\u3040-\u309F\u30A0-\u30FF]/.test(str)
  },

  /**
   * 检查字符串是否全是片假名
   * @param {string} str - 要检查的字符串
   * @returns {boolean} 是否全是片假名
   * @private
   */
  _isAllKatakana(str) {
    if (!str)
      return false
    return /^[\u30A0-\u30FF]+$/.test(str)
  },

  /**
   * 从 replacement 中初步提取所有符合注音格式的词条，只要格式符合 kanji（reading）且不为空
   * @param {string} replacement - 要提取词条的文本
   * @returns {Array<{kanji: string, reading: string, fullPattern: string}>} 提取出的词条数组
   * @private
   */
  _extractRubyCandidates(replacement) {
    const results = []
    for (const match of replacement.matchAll(this._regex.extractCandidates)) {
      const kanji = match[1]
      const reading = match[2]

      // 检查 kanji 和 reading 都不为空
      if (!kanji || !reading) {
        console.warn(`[WARN] [提取词条] 跳过无效词条: kanji="${kanji}", reading="${reading}"`)
        continue
      }

      const fullPattern = `${kanji}（${reading}）`
      results.push({ kanji, reading, fullPattern })
    }
    return results
  },
}

export default RubyConverter
