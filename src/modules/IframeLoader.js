/**
 * @module IframeLoader
 * @description 负责 iframe 的加载策略管理
 * - lazy (懒加载) 策略: 结合浏览器原生 loading="lazy" 和 CSS 样式，实现高性能懒加载及视觉反馈
 * - click (点击加载) 策略: 将 iframe 替换为点击后才加载的占位符，实现极致的初始加载性能
 * - eager (默认) 策略: 不做任何干预，由浏览器默认处理
 */

const IframeLoader = {
  _config: {
    MODULE_ENABLED: true,
    IFRAME_LOAD_ENABLED: true, // 可以从 setting 中获取覆盖
    IFRAME_SELECTOR: 'iframe[src*="richlink.blogsys.jp"]',
    PLACEHOLDER_CLASS: 'iframe-placeholder',
    LOADING_CLASS: 'is-loading',
    CLICKABLE_CLASS: 'is-clickable',
    STYLES: `
      @keyframes iframe-spinner-rotation { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
      .iframe-placeholder { position: relative; display: inline-block; vertical-align: top; background-color: #f9f9f9; box-sizing: border-box; margin: 8px 0; }
      .is-loading::after { opacity: 0.9; content: ''; position: absolute; top: 50%; left: 50%; width: 32px; height: 32px; margin-top: -16px; margin-left: -16px; border: 4px solid #ccc; border-top-color: #3B82F6; border-radius: 50%; animation: iframe-spinner-rotation 1s linear infinite; }
      .is-clickable { opacity: 0.9; display: inline-grid; place-items: center; color: #ccc !important; font-weight: bold; font-size: 16px; cursor: pointer; transition: background-color 0.2s, color 0.2s; -webkit-user-select: none; user-select: none; }
      .is-clickable:hover { opacity: 0.9; color: #3B82F6 !important; background-color: #f4f8ff; }
      @media screen and (max-width: 870px) { .iframe-placeholder { max-width: 350px !important; height: 105px !important; } }
      @media screen and (min-width: 871px) { .iframe-placeholder { max-width: 580px !important; height: 120px !important; } }
    `,
  },

  /**
   * 初始化模块
   * @param {object} options - 配置选项
   */
  init(options = {}) {
    if (!this._config.MODULE_ENABLED)
      return

    // 合并配置
    Object.assign(this._config, options)

    // 注入样式
    GM_addStyle(this._config.STYLES)
  },

  /**
   * 在指定容器中替换 iframe 为懒加载或点击加载版本
   * @param {HTMLElement} container - 包含 iframe 的容器元素
   */
  replaceIframesInContainer(container) {
    if (!this._config.MODULE_ENABLED)
      return

    const iframes = container.querySelectorAll(this._config.IFRAME_SELECTOR)
    if (iframes.length === 0)
      return

    this._config.IFRAME_LOAD_ENABLED ? this._processForLazyLoad(iframes) : this._processForClickToLoad(iframes)
  },

  /**
   * 使用 IntersectionObserver 实现高性能、高可靠的懒加载
   * @param {NodeList} iframes - iframe 元素集合
   * @private
   */
  _processForLazyLoad(iframes) {
    // 1. 创建一个观察者实例，用于监视所有iframe占位符
    const observer = new IntersectionObserver(
      (entries, obs) => {
        entries.forEach((entry) => {
          // 当占位符进入或即将进入视口时
          if (entry.isIntersecting) {
            const placeholder = entry.target

            // 动态创建真正的 iframe
            const iframe = document.createElement('iframe')
            iframe.src = placeholder.dataset.src
            iframe.setAttribute('style', placeholder.dataset.style)
            iframe.setAttribute('frameborder', '0')
            iframe.setAttribute('scrolling', 'no')
            iframe.style.opacity = '0' // 初始保持透明

            iframe.addEventListener(
              'load',
              () => {
                placeholder.classList.remove(this._config.LOADING_CLASS)
                iframe.style.opacity = '1'
              },
              { once: true },
            )

            // 将 iframe 添加到占位符中
            placeholder.appendChild(iframe)

            // 关键优化：一旦处理完毕，就立刻停止观察此占位符，释放资源
            obs.unobserve(placeholder)
          }
        })
      },
      {
        // 预加载边距：元素距离视口底部200px时即开始加载
        rootMargin: '200px 0px',
      },
    )

    // 2. 遍历所有找到的 iframe，用占位符替换它们
    iframes.forEach((iframe) => {
      const placeholder = document.createElement('div')
      placeholder.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.LOADING_CLASS}`

      const originalStyle = iframe.getAttribute('style') || ''
      placeholder.setAttribute('style', originalStyle)

      // 将 iframe 的重要信息缓存到占位符的 data-* 属性中
      placeholder.dataset.src = iframe.src
      placeholder.dataset.style = originalStyle

      // 用占位符替换原始 iframe
      iframe.replaceWith(placeholder)

      // 让观察者开始监视这个新的占位符
      observer.observe(placeholder)
    })
  },

  /**
   * 处理点击加载策略
   * @param {NodeList} iframes - iframe 元素集合
   * @private
   */
  _processForClickToLoad(iframes) {
    iframes.forEach((iframe) => {
      if (iframe.parentElement.classList.contains(this._config.PLACEHOLDER_CLASS))
        return

      const originalSrc = iframe.src
      const originalStyle = iframe.getAttribute('style') || ''

      const placeholder = document.createElement('div')
      placeholder.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.CLICKABLE_CLASS}`
      placeholder.textContent = '▶ 関連記事を読み込む'
      placeholder.setAttribute('style', originalStyle)

      placeholder.addEventListener(
        'click',
        () => {
          const newIframe = document.createElement('iframe')
          newIframe.src = originalSrc
          newIframe.setAttribute('style', originalStyle)
          newIframe.setAttribute('frameborder', '0')
          newIframe.setAttribute('scrolling', 'no')

          const loadingWrapper = document.createElement('div')
          loadingWrapper.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.LOADING_CLASS}`
          loadingWrapper.setAttribute('style', originalStyle)

          newIframe.style.opacity = '0'
          loadingWrapper.appendChild(newIframe)

          newIframe.addEventListener(
            'load',
            () => {
              loadingWrapper.classList.remove(this._config.LOADING_CLASS)
              newIframe.style.opacity = '1'
            },
            { once: true },
          )

          placeholder.replaceWith(loadingWrapper)
        },
        { once: true },
      )

      iframe.replaceWith(placeholder)
    })
  },
}

export default IframeLoader
